@file:Suppress("TooGenericExceptionCaught")

package com.superhexa.supervision.feature.alipay

import android.content.Context
import android.content.Intent
import android.os.DeadObjectException
import android.os.RemoteException
import android.text.TextUtils
import androidx.core.net.toUri
import androidx.lifecycle.LifecycleOwner
import com.alibaba.fastjson.JSON
import com.alipay.android.glassapi.AlipayScanResult
import com.alipay.android.glassapi.DeviceRequireData
import com.alipay.android.glassapi.DeviceResponseData
import com.alipay.api.AlipayGlassSdk
import com.alipay.api.AlipayGlassV2Sdk
import com.alipay.api.callback.BindStatusCallBack
import com.alipay.api.callback.BindingCallBack
import com.alipay.api.callback.GlassSDKCallback
import com.alipay.api.callback.IScanPayCallbck
import com.alipay.glass.config.GlasspayConfig
import com.alipay.glass.rpc.DevTool
import com.google.gson.Gson
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.alipay.data.IntentContent
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.o95.O95StateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.aivs.MiWearAivsHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.record.MiWearRecordHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.thirdparty.MiWearThirdPartyAppHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.tools.SBCCodec
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.tencent.mmkv.MMKV
import com.xiaomi.aivs.AiSpeechEngine
import com.xiaomi.aivs.engine.listener.IAlipayListener
import com.xiaomi.aivs.engine.process.TtsDependency
import com.xiaomi.aivs.engine.state.AudioFocusState
import com.xiaomi.wear.protobuf.nano.ThirdpartyAppProtos.AppStatus.DISCONNECTED
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class AlipaySDKManager private constructor() : IAlipayListener, AutoCloseable {

    // region 常量定义
    companion object {
        private const val TAG = "AlipaySDKManager"
        const val PACKAGE_NAME = "com.eg.android.AlipayGGlasses"
        const val DEMO_MODE_SCHEME = "glasses://alipay:9999/alipayDemoMode"
        private const val AUDIO_SAMPLE_RATE = 16000
        private const val AUDIO_CHANNELS = 1
        private const val MAX_AUDIO_CACHE_SIZE = 10 * 1024 * 1024 // 10MB
        private const val LOW_BATTERY = 10
        private const val DELAY_TIME = 1000L

        private const val AUDIO_SAVE_DIR = "alipay_audio" // 音频保存目录
        private const val ROW_SAVE_DIR = "row_audio" // 音频保存目录
        private const val PCM_FILE_PREFIX = "alipay_audio_" // 文件名前缀
        private const val PCM_FILE_EXTENSION = ".pcm" // 文件扩展名
        private var currentAudioFile: File? = null // 当前音频文件对象
        private var audioFileOutputStream: FileOutputStream? = null // 文件输出流
        // private var isFirstIntent = true // 新增：标记首次意图

        val INSTANCE: AlipaySDKManager by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            AlipaySDKManager().also {
                it.startAudioProcessor()
            }
        }
    }
    // endregion

    // region 状态管理
    sealed class SdkState {
        object Uninitialized : SdkState()
        object Initialized : SdkState()
        data class Error(val cause: Throwable) : SdkState()
    }

    sealed class BindingState {
        object Unbound : BindingState()
        object Binding : BindingState()
        data class Bound(val token: String?) : BindingState()
        data class Error(val code: String?) : BindingState()
    }

    private val _sdkState = MutableStateFlow<SdkState>(SdkState.Uninitialized)
    val sdkState = _sdkState.asStateFlow()

    private val _bindingState = MutableStateFlow<BindingState>(BindingState.Unbound)
    val bindingState = _bindingState.asStateFlow()
    private val isInitialized get() = _sdkState.value is SdkState.Initialized
    private val isBound get() = _bindingState.value is BindingState.Bound
    private val coroutineExceptionHandler = CoroutineExceptionHandler { _, exception ->
        Timber.i("$TAG Caught: $exception")
    }

    private var ttsId: String? = null

    @OptIn(ExperimentalCoroutinesApi::class)
    private val audioProcessingScope = CoroutineScope(
        SupervisorJob() +
            Dispatchers.IO.limitedParallelism(1) + // 强制单线程
            coroutineExceptionHandler
    )
    private val scope = CoroutineScope(
        SupervisorJob() + Dispatchers.IO + coroutineExceptionHandler
    )
    private val audioBuffer = Channel<ByteArray>(Channel.UNLIMITED)
    // endregion

    // region 依赖组件
    private val paymentCache = PaymentCache()
    private var startTime: Long? = null
    private var dialogId: String? = null
    private var sessionId: String? = null
    private val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    private val decorator by lazy {
        if (DeviceModelManager.isMijiaO95SeriesDevice(bondDevice?.model)) {
            DecoratorUtil.getDecorator<O95StateLiveData>(bondDevice)
        } else {
            null
        }
    }
    private val deviceStateLiveData by lazy { decorator?.liveData }
    // endregion

    // region 回调定义
    private val scanCallBack = object : IScanPayCallbck {
        override fun onScanSuccess(result: AlipayScanResult?) {
            Timber.i("onScanSuccess $result")
            AiSpeechEngine.INSTANCE.scanPayResult(0, result.toString())
        }

        override fun onScanFailed(code: Int) {
            Timber.i("onScanFailed $code")
            AiSpeechEngine.INSTANCE.scanPayResult(code, "onScanFailed")
        }

        override fun onScanProcess(code: Int, message: String?) {
            Timber.i("onScanProcess $code,$message")
            AiSpeechEngine.INSTANCE.scanPayResult(code, message)
        }

        /**
         * 错误码 误描述     解决方案
         * 1001 扫码超时    检查二维码是否破损并重试
         * 1002 摄像头异常  重启设备，检查摄像头是否正常
         * 1003 网络异常    检查网络是否正常
         * 1004 系统繁忙    1、绑定失败重试超过100次；2、码路由信息解析异常
         * 1005 用户取消    扫码用户主动关闭，无需处理
         * 1006 暂不支持    扫描的码更换支持的支付宝收款码、经营码等
         * 1007 手机端解绑  重新绑定设备
         * 4000 1、系统繁忙，请稍后再试2、手机端解绑3、付款功能关闭4、支付超出限额   根据errorMessage具体处理
         * 8000 支付结果未知，  请与商家确认 联系商户确认支付结果
         * 9000 支付成功     无需处理
         */
        override fun onPayResult(code: String?, message: String?) {
            Timber.i("onPayResult $code,$message")
            AiSpeechEngine.INSTANCE.onPayResult(code, message)
        }
    }

    private val bindStatusCallBack = BindStatusCallBack { bindStatus ->
        Timber.i("onGetBindStatus $bindStatus")
        _bindingState.value = if (bindStatus) BindingState.Bound(null) else BindingState.Unbound
    }

    private val alipayCallback = object : GlassSDKCallback {
        override fun onStartRecording(token: String?) = Timber.i("onStartRecording:$token")
        override fun onStopRecording() {
            Timber.i("onStopRecording")
        }

        override fun onStartPlayback(text: String?): String {
            Timber.i("onStartPlayback:$text")
            return ""
        }

        override fun sendDeviceLink(data: DeviceRequireData?): DeviceResponseData? {
            Timber.i("sendDeviceLink:$data")
            data?.let { sendDeviceLinkAsync(JSON.toJSONString(it)) }
            return DeviceResponseData()
        }

        override fun sendDeviceLinkAsync(data: String?): Int {
            Timber.i("sendDeviceLinkAsync:$data")
            data?.let {
                return runBlocking {
                    try {
                        val bytes = it.toByteArray(Charsets.UTF_8)
                        MiWearThirdPartyAppHandler.sendPhoneMessage(
                            decorator,
                            packageName = PACKAGE_NAME,
                            fingerprint = bytes,
                            content = bytes
                        )
                    } catch (e: Exception) {
                        Timber.e(e, "Failed to send phone message")
                        -1
                    }
                }
            }
            return -1
        }

        override fun onWearDetection(): Boolean = false

        // Q9990("AlipayGlassSDKExit", 退出，⼚商侧语⾳助⼿退出技能，恢复到 Idle 状态 等待下次唤醒
        override fun onErrorEvent(code: String?, message: String?) {
            Timber.i("onErrorEvent:$code,$message")
            val isQ9990 = TextUtils.equals(code, "Q9990")
            if (message != null && !isQ9990) {
                ttsId = AiSpeechEngine.INSTANCE.startTts(message)
                TtsDependency.onReceiveTtsSpeech(ttsId)
            }
            if (isQ9990) {
                TtsDependency.processDependencyTask(ttsId) {
                    scope.launch {
                        delay(DELAY_TIME) // 防止语音没有播放 延长1s退出小爱
                        AiSpeechEngine.INSTANCE.alipayExi("onErrorEvent $message")
                    }
                }
            }
            AiSpeechEngine.INSTANCE.alipayErrorEvent(code, message)
        }

        override fun onViIntentResult(
            bizType: String?,
            dataJson: String?,
            extInfo: MutableMap<String, Any>?
        ) {
            Timber.i("onViIntentResult:$dialogId,$bizType,$dataJson,$extInfo")
            if (dataJson.isNullOrEmpty() || dialogId.isNullOrEmpty()) {
                Timber.i("dataJson or dialogId is null or empty")
                return
            }

            // 过滤首次意图
//            if (isFirstIntent) {
//                Timber.i("首次意图已过滤")
//                isFirstIntent = false // 重置标志
//                return
//            }

            try {
                val content = Gson().fromJson(dataJson, IntentContent::class.java)
                val query = content.query
                if (query.isNotNullOrEmpty()) {
                    AiSpeechEngine.INSTANCE.restartTimer(
                        "onViIntentResult",
                        AiSpeechEngine.INSTANCE.countdownTime()
                    )
                    updateChatHistory(query, 1)
                }
            } catch (e: Exception) {
                Timber.e(e, "Failed to parse dataJson")
            }
        }
    }

    private val bindingCallBack = object : BindingCallBack {
        override fun onGetBindingSchemeSuccess(bindingScheme: String?, token: String?) {
            Timber.i("onGetBindintSchemeSuccess:$bindingScheme,$token")

            bindingScheme?.let {
                val uri = it.toUri()
                val intent = Intent(Intent.ACTION_VIEW, uri).apply {
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                LibBaseApplication.instance.startActivity(intent)
                AudioFocusState.doFocusRequest(
                    AiSpeechEngine.INSTANCE.appContext,
                    "alipay startBinding"
                )
            }
        }

        override fun onBindingSuccess() {
            Timber.i("onBindingSuccess")
            _bindingState.value = BindingState.Bound(null)
            AudioFocusState.doFocusAbandon(
                context = AiSpeechEngine.INSTANCE.appContext,
                reason = "onBindingSuccess"
            )
        }

        override fun onBindingFailed(errorCode: String?) {
            _bindingState.value = BindingState.Error(errorCode)
            Timber.i("onBindingFailed:$errorCode")
        }

        override fun getReturnUrl(): String = ""

        override fun onGetBindExtInfo(
            extInfo: MutableMap<String, Any>?,
            token: String?
        ): Map<String, Any> = mapOf()
    }

    // region 初始化与生命周期
    fun initialize(
        context: Context,
        lifecycleOwner: LifecycleOwner? = null,
        sn: String
    ): Boolean {
        Timber.i("initialize $isInitialized")
        com.alipay.glass.log.Log.setLogger(AlipayGlassLog())
        return synchronized(this) {
            try {
                if (BuildConfig.DEBUG) {
                    DevTool.get().curEnv = getCurrentEnv()
                } else {
                    DevTool.get().curEnv = DevTool.Envs.ENV_ONLINE
                }
                Timber.i("initialize curEnv:${DevTool.get().curEnv}")
                AlipayGlassV2Sdk.initSDK(context, createAlipayInitConfig(sn), alipayCallback)
                _sdkState.value = SdkState.Initialized
                scope.launch { getBindStatus(true) }
                lifecycleOwner?.let { setupObservers(it) }
                AiSpeechEngine.INSTANCE.addAlipayObserver(TAG, lifecycleOwner?.lifecycle, this)
                true
            } catch (e: Throwable) {
                Timber.e(e, "支付宝SDK初始化失败")
                _sdkState.value = SdkState.Error(e)
                false
            }
        }
    }

    override fun close() {
        scope.cancel()
        audioBuffer.close()
        AiSpeechEngine.INSTANCE.removeAlipayObserver(TAG)
    }
    // endregion

    // region 音频处理
    val audioDataAccumulator = mutableListOf<ByteArray>()
    var currentSize = 0
    private fun startAudioProcessor() {
        audioProcessingScope.launch {
            audioBuffer.receiveAsFlow().collect { audioData ->
                try {
                    if (BuildConfig.DEBUG) {
                        // 检查内存限制
                        if (currentSize + audioData.size > MAX_AUDIO_CACHE_SIZE) {
                            Timber.w("Audio cache size exceeded limit, clearing cache")
                            audioDataAccumulator.clear()
                            currentSize = 0
                        }

                        // 累积音频数据
                        audioDataAccumulator.add(audioData)
                        currentSize += audioData.size
                    }

                    // 处理音频数据
                    processAudioData(audioData)
                } catch (e: Exception) {
                    Timber.e(e, "Error processing audio data")
                }
            }
        }
    }

    private suspend fun processAudioData(data: ByteArray) {
        postSBCPcmData(data) { decodedData ->
            audioProcessingScope.launch {
                sendAudioToAlipay(decodedData)
                //               AiSpeechEngine.INSTANCE.receivePcmData(decodedData)
//                audioHandler.handleAudioCallback(decodedData) {
//                    audioHandler.saveAndPlayAudio(LibBaseApplication.instance)
//                }
            }
        }
    }
    // endregion

    // region 支付宝回调实现
    override fun offlineCloudStop(startTime: Long) {
        Timber.i("offlineCloudStop：$startTime")
        this.startTime = startTime
    }

    override fun updateDialogId(dialogId: String?) {
        Timber.i("updateDialogId：$dialogId")
        this.dialogId = dialogId
    }

    private fun startScanPay(
        sessionId: String?,
        dialogId: String,
        asrResult: String,
        isDemoMode: Boolean = false
    ) {
        Timber.i("scanPay：$sessionId,$dialogId,$asrResult,$isInitialized,$isBound,$isDemoMode")
        this.dialogId = dialogId
        this.sessionId = sessionId
        // isFirstIntent = true
        if (!isInitialized || !isBound) {
            Timber.i("发送失败：SDK未初始化或服务未绑定")
            return
        }

        if (getBatteryInfo() <= LOW_BATTERY) {
            val reason = "电量低 无法开启扫码支付"
            startTTS(reason) {
                AiSpeechEngine.INSTANCE.finishSession(reason)
            }
            return
        }

        paymentCache.confirmPaymentIntent("confirmPaymentIntent")
        startTime?.let {
            sendFirstVoiceData(paymentCache.getFirstVoiceCache(it)) {
                paymentCache.clearFirstVoiceCache("confirmPaymentIntent")
                // 新增：首段语音发送完成后处理待发送缓冲区
                val pendingData = paymentCache.takePendingVoiceData()
                if (pendingData.isNotEmpty()) {
                    sendPendingVoiceData(pendingData)
                }
                // 标记首段语音已发送完
                paymentCache.markFirstVoiceSent()
            }
        }
        scope.launch {
            val extParams: MutableMap<String, Any> = HashMap()
            if (isDemoMode) {
                extParams[GlasspayConfig.USE_DEMOMODE] = true
            }
            extParams["PAY_INIT_ASR"] = asrResult
            Timber.i("scanPay extParams：$extParams")
            AlipayGlassV2Sdk.scanpay(extParams, scanCallBack)
        }
    }

    private fun getBatteryInfo(): Int {
        val batteryCapacity = deviceStateLiveData?.value?.let { stateData ->
            if (stateData.deviceState.isChannelSuccess()) {
                stateData.deviceStatus?.battery?.capacity ?: 0
            } else {
                0
            }
        } ?: 0
        Timber.i("getBatteryInfo $batteryCapacity")
        return batteryCapacity
    }

    override fun scanPay(sessionId: String?, dialogId: String, asrResult: String) {
        Timber.i("scanPay")
        startScanPay(sessionId, dialogId, asrResult)
    }

    /**
     * 演示模式
     */
    override fun scanPayWithDemoMode(sessionId: String?, dialogId: String, asrResult: String) {
        Timber.i("scanPayWithDemoMode")
        startScanPay(sessionId, dialogId, asrResult, true)
    }

    override fun alipayExi(reason: String?) {
        Timber.i("alipayExi $reason")
        ttsId = null
        this.dialogId = null
        this.sessionId = null
        cleanupAudioFiles()
        saveRawAudioToFile(audioDataAccumulator, LibBaseApplication.instance)
        paymentCache.clear("alipayExi")
        AiSpeechEngine.INSTANCE.finishSession(reason)
    }

    override fun alipayInterrupt(reason: String?) {
        Timber.i("alipayInterrupt $reason")
        sendAlipayExitCommand()
        ttsId = null
        this.dialogId = null
        this.sessionId = null
        cleanupAudioFiles()
        saveRawAudioToFile(audioDataAccumulator, LibBaseApplication.instance)
        paymentCache.clear("alipayInterrupt")
    }

    private fun sendAlipayExitCommand() {
        Timber.i("sendAlipayExitCommand")
        val configParam: MutableMap<String, Any> = HashMap()
        configParam[GlasspayConfig.KEY_COMMAND_CODE] = GlasspayConfig.CommandCode.EXIT
        AlipayGlassV2Sdk.command(configParam)
    }

    private fun startTTS(tts: String, function: (() -> Unit)? = null) {
        scope.launch {
            ttsId = AiSpeechEngine.INSTANCE.startTts(tts)
            Timber.w("startTts $tts,$ttsId")
            updateChatHistory(tts, 0)
            val taskId = ttsId ?: System.currentTimeMillis().toString()
            TtsDependency.onReceiveTtsSpeech(taskId)
            TtsDependency.processDependencyTask(taskId) {
                Timber.i("startTts play done")
                function?.invoke()
            }
        }
    }

    override fun startFirstVoice() {
        paymentCache.startFirstVoiceCache()
    }

    override fun clearVoiceData() {
        Timber.i("clearVoiceData")
        paymentCache.clear("clearVoiceData")
    }

    override fun sendVoiceData(bytes: ByteArray) {
        scope.launch {
            try {
                if (paymentCache.cacheFirstVoice) {
                    paymentCache.cacheFirstVoiceData(bytes)
                } else if (paymentCache.getIsPaymentIntent()) {
                    // 根据状态决定直接发送或缓存
                    if (paymentCache.isFirstVoiceSent) {
                        sendAudioToAlipay(bytes)
                    } else {
                        paymentCache.cacheVoiceData(bytes)
                    }
                }
            } catch (e: RemoteException) {
                Timber.e("远程调用异常: ${e.message}")
            } catch (e: DeadObjectException) {
                Timber.e("Binder失效: ${e.message}")
            } catch (e: Exception) {
                Timber.e("未知发送异常: ${e.message}")
            }
        }
    }
    // endregion

    // region 绑定管理
    fun startBinding() {
        Timber.i("startBinding：$isInitialized,$isBound")
        if (!isInitialized) return

        scope.launch {
            Timber.i("startBinding call")
            _bindingState.value = BindingState.Binding
            val map = mutableMapOf<String, Any>().apply {
                put(GlasspayConfig.BIND_RETURN_URL, DEMO_MODE_SCHEME)
            }
            AlipayGlassV2Sdk.startBinding(map, bindingCallBack)
        }
    }

    fun unBind(callback: (Boolean) -> Unit?) {
        scope.launch {
            AiSpeechEngine.INSTANCE.playVoiceFinish()
            if (isInitialized) {
                val unbind = AlipayGlassV2Sdk.unbind()
                callback?.invoke(unbind)
                Timber.i("unBind $unbind")
                _bindingState.value = BindingState.Unbound
            }
        }
    }

    /**
     * needCloudStatus 是否需要查询云端的状态
     */
    suspend fun getBindStatus(needCloudStatus: Boolean = true): Boolean =
        withContext(Dispatchers.IO) {
            if (!isInitialized) {
                Timber.i("getBindStatus SDK未初始化")
                return@withContext false
            }

            AlipayGlassV2Sdk.getBindStatus(bindStatusCallBack, needCloudStatus)
            Timber.i("getBindStatus $isBound，$needCloudStatus")
            isBound
        }
    // endregion

    // region 辅助方法
    private fun setupObservers(lifecycleOwner: LifecycleOwner) {
        MiWearAivsHandler.startTtsCallback.observe(lifecycleOwner) {
            startTTS(it)
        }

        MiWearRecordHandler.voiceprintPcmDataCallback.observe(lifecycleOwner) { pcmData ->
            scope.launch {
                Timber.i("send pcmData format ${pcmData.audioFormat} size ${pcmData.audioData.size}")
                audioBuffer.send(pcmData.audioData)
            }
        }

        MiWearThirdPartyAppHandler.reportMessageContentCallback.observe(lifecycleOwner) { content ->
            Timber.i("onGateDataReceived content:$content")
            content?.content?.toString(Charsets.UTF_8)?.let { str ->
                Timber.i("onGateDataReceived str:$str")
                val response = AlipayGlassV2Sdk.onGateDataReceived(str)
                Timber.i("onGateDataReceived response:$response")
                alipayCallback.sendDeviceLinkAsync(response)
            }
        }

        MiWearThirdPartyAppHandler.reportWearAppStatusCallback.observe(lifecycleOwner) { appStatus ->
            Timber.i("appStatusCallback:$appStatus")
            if (DISCONNECTED == appStatus?.status) {
                val configParam: MutableMap<String, Any> = HashMap()
                configParam[GlasspayConfig.KEY_COMMAND_CODE] = GlasspayConfig.CommandCode.EXIT
                configParam["source"] = "GlassAppDisconnect"
                AlipayGlassV2Sdk.command(configParam)
            }
        }
    }

    private fun updateChatHistory(str: String, type: Int) {
        Timber.i("updateChatHistory:$sessionId,$dialogId,$type,$str")
        if (dialogId.isNotNullOrEmpty() && sessionId.isNotNullOrEmpty()) {
            AiSpeechEngine.INSTANCE.addToChatHistory(
                sessionId,
                "dialogId_$dialogId",
                content = str,
                type = type
            )
        } else {
            Timber.i("updateChatHistory:invalid Id")
        }
    }

    private suspend fun sendAudioToAlipay(
        bytes: ByteArray,
        extInfo: MutableMap<String, String> = mutableMapOf()
    ) {
        Timber.i("sendAudioToAlipay:${bytes.size},$extInfo")
        AlipayGlassV2Sdk.receiveDenoiseAudio(
            bytes,
            "PCM",
            AlipayGlassSdk.AVSampleFormat.AV_SAMPLE_FMT_S16.sampleType,
            AUDIO_SAMPLE_RATE,
            AUDIO_CHANNELS,
            extInfo
        )
        savePcmData(bytes)
    }

    private fun sendFirstVoiceData(bytes: List<ByteArray>, callback: (() -> Unit)? = null) {
        if (!isInitialized || !isBound) {
            Timber.e("sendFirstVoiceData 发送失败：SDK未初始化或服务未绑定 ${bytes.size}")
            return
        }
        Timber.i("sendFirstVoiceData call")
        scope.launch {
            try {
                bytes.forEach {
                    sendAudioToAlipay(
                        it,
                        mutableMapOf<String, String>().apply {
                            put("flowNode", "AROUSE")
                        }
                    )
                }
                withContext(Dispatchers.Main) {
                    Timber.i("音频发送成功，大小: ${bytes.size}")
                    callback?.invoke()
                }
            } catch (e: Exception) {
                Timber.e(e, "发送首段语音数据失败")
            }
        }
    }

    private suspend fun postSBCPcmData(data: ByteArray, callback: (ByteArray) -> Unit) {
        try {
            SBCCodec.decodeSBC(data)?.let(callback) ?: Timber.i("msbc decode fail.")
        } catch (e: Exception) {
            Timber.e(e, "SBC decoding error")
        }
    }

    private fun saveRawAudioToFile(audioDataList: List<ByteArray>, context: Context) {
        if (!BuildConfig.DEBUG) return
        scope.launch {
            try {
                val totalSize = audioDataList.sumOf { it.size }
                if (totalSize == 0) return@launch
                // 创建目录（如果不存在）
                val audioDir =
                    File(LibBaseApplication.instance.getExternalFilesDir(null), AUDIO_SAVE_DIR)
                if (!audioDir.exists()) audioDir.mkdirs()

                val output = ByteArrayOutputStream(totalSize)
                audioDataList.forEach { output.write(it) }

                val timeStamp =
                    SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
                val fileName = "raw_audio_$timeStamp.pcm"
                val file = File(audioDir, fileName)

                FileOutputStream(file).use { fos ->
                    fos.write(output.toByteArray())
                }
                audioDataAccumulator.clear()
                currentSize = 0
                Timber.i("原始音频已保存到: ${file.absolutePath}")
            } catch (e: Exception) {
                audioDataAccumulator.clear()
                currentSize = 0
                Timber.e("保存原始音频失败 $e")
            }
        }
    }

    private fun createAlipayInitConfig(sn: String?): MutableMap<String, Any> {
        return mutableMapOf<String, Any>().apply {
            put(GlasspayConfig.ISV_NAME, "xiaomi")
            put(GlasspayConfig.DEVICE_MODEL, "xiaomi-glass")
            put(GlasspayConfig.DEVICE_SN, sn ?: "")
            put(GlasspayConfig.GLASS_SUPPORT_NETWORK, false)
            put(GlasspayConfig.PHONE_SYSTYPE, "Android")
            put(GlasspayConfig.GLASS_PAYCONFIG, "{}")
            Timber.i("createAlipayInitConfig:$this")
        }
    }
    // endregion

    // 新增：发送待发送缓冲区的数据
    private fun sendPendingVoiceData(dataList: List<ByteArray>) {
        scope.launch {
            try {
                dataList.forEach { bytes ->
                    sendAudioToAlipay(bytes)
                }
                Timber.i("待发送缓冲区数据已处理，数量: ${dataList.size}")
            } catch (e: Exception) {
                Timber.e(e, "发送待发送缓冲区数据失败")
            }
        }
    }

    private fun cleanupAudioFiles() {
        if (!BuildConfig.DEBUG) return
        try {
            audioFileOutputStream?.close() // 关闭流
            audioFileOutputStream = null
            Timber.i("PCM audio saved to: ${currentAudioFile?.absolutePath}") // 日志记录路径
            currentAudioFile = null
        } catch (e: Exception) {
            Timber.e(e, "Failed to cleanup audio files")
        }
    }

    private fun savePcmData(data: ByteArray) {
        if (!BuildConfig.DEBUG) return
        try {
            // 创建目录（如果不存在）
            val audioDir =
                File(LibBaseApplication.instance.getExternalFilesDir(null), AUDIO_SAVE_DIR)
            if (!audioDir.exists()) audioDir.mkdirs()

            // 首次写入时创建新文件（按时间戳命名）
            if (currentAudioFile == null) {
                val timeStamp =
                    SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
                currentAudioFile = File(audioDir, "$PCM_FILE_PREFIX$timeStamp$PCM_FILE_EXTENSION")
                audioFileOutputStream = FileOutputStream(currentAudioFile)
                Timber.e("savePcmData $currentAudioFile")
            }

            // 写入数据
            audioFileOutputStream?.write(data)
        } catch (e: Exception) {
            Timber.e(e, "Failed to save PCM data")
            cleanupAudioFiles() // 出错时清理资源
        }
    }

    fun changeEnv(type: DevTool.Envs) {
        Timber.i("changeEnv ${type.name},${type.ordinal}")
        MMKV.defaultMMKV().encode("alipayEnv", type.ordinal)
        DevTool.get().curEnv = type
    }

    private fun getCurrentEnv(): DevTool.Envs {
        val savedOrdinal =
            MMKV.defaultMMKV().decodeInt("alipayEnv", DevTool.Envs.ENV_ONLINE.ordinal)
        val result = if (savedOrdinal != -1) {
            DevTool.Envs.values()[savedOrdinal] // 通过 ordinal 获取对应枚举
        } else {
            DevTool.Envs.ENV_ONLINE
        }
        Timber.i("getCurrentEnv ${result.name},${result.ordinal}")
        return result
    }
}
