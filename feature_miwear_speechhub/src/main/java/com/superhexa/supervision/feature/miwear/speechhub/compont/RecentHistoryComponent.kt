@file:Suppress("MagicNumber")

package com.superhexa.supervision.feature.miwear.speechhub.compont

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.google.accompanist.flowlayout.FlowRow
import com.superhexa.supervision.feature.miwear.speechhub.R
import com.superhexa.supervision.library.base.basecommon.compose.extension.clickDebounceNoEffect
import com.superhexa.supervision.library.base.basecommon.theme.Dp_1
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_14
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_33
import com.superhexa.supervision.library.base.basecommon.theme.Dp_8
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13

private const val SEARCH_HISTORY_ITEM_LIMIT = 20

@Suppress("LongMethod")
@Composable
fun RecentSearchScreen(
    visible: Boolean = true,
    allHistory: List<String>,
    onSearchHistoryClick: (String) -> Unit,
    onItemDelete: (String) -> Unit,
    onAllItemDelete: () -> Unit
) {
    if (!visible) return

    val displayHistory = remember { mutableStateListOf<String>() }
    var isEditing by remember { mutableStateOf(false) }

    LaunchedEffect(allHistory.size) {
        displayHistory.clear()
        displayHistory.addAll(allHistory.take(SEARCH_HISTORY_ITEM_LIMIT))
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .padding(horizontal = Dp_28)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(Dp_20),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = stringResource(R.string.text_recent_use),
                style = TextStyle(
                    fontSize = Sp_13,
                    fontWeight = FontWeight.W400,
                    textAlign = TextAlign.Center
                ),
                color = colorResource(R.color.white_60)
            )

            if (isEditing) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text(
                        text = stringResource(R.string.text_delete_all),
                        style = TextStyle(
                            fontSize = Sp_13,
                            fontWeight = FontWeight.W400,
                            textAlign = TextAlign.Center
                        ),
                        color = colorResource(R.color.white_60),
                        modifier = Modifier.clickDebounceNoEffect {
                            displayHistory.clear()
                            isEditing = false
                            onAllItemDelete.invoke()
                        }
                    )
                    Spacer(modifier = Modifier.width(Dp_12))
                    Box(
                        modifier = Modifier
                            .width(Dp_1)
                            .height(Dp_12)
                            .background(colorResource(R.color.white))
                    )
                    Spacer(modifier = Modifier.width(Dp_12))
                    Text(
                        text = stringResource(R.string.text_complete),
                        style = TextStyle(
                            fontSize = Sp_13,
                            fontWeight = FontWeight.W400,
                            textAlign = TextAlign.Center
                        ),
                        color = colorResource(R.color.color_55D8E4),
                        modifier = Modifier.clickDebounceNoEffect { isEditing = false }
                    )
                }
            } else {
                Icon(
                    painter = painterResource(R.drawable.icon_delete),
                    contentDescription = stringResource(R.string.libs_delete),
                    modifier = Modifier
                        .size(Dp_20)
                        .clickDebounceNoEffect { isEditing = true },
                    tint = colorResource(R.color.white)
                )
            }
        }
        Spacer(modifier = Modifier.height(Dp_12))

        FlowRow(
            modifier = Modifier.fillMaxWidth(),
            mainAxisSpacing = Dp_8,
            crossAxisSpacing = Dp_8
        ) {
            displayHistory.forEach { item ->
                SearchItem(
                    text = item,
                    isEditing = isEditing,
                    onClick = { onSearchHistoryClick(item) },
                    onDelete = {
                        displayHistory.remove(item)
                        onItemDelete(item)
                    }
                )
            }
        }
    }
}

@Composable
fun SearchItem(
    text: String,
    isEditing: Boolean,
    onClick: () -> Unit,
    onDelete: () -> Unit
) {
    Box(
        modifier = Modifier
            .height(Dp_33)
            .border(
                width = Dp_1,
                color = colorResource(R.color.white_10),
                shape = RoundedCornerShape(Dp_12)
            )
            .padding(horizontal = Dp_12, vertical = Dp_8)
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            Text(
                text = text,
                style = TextStyle(
                    fontSize = Sp_13,
                    fontWeight = FontWeight.W400,
                    textAlign = TextAlign.Center
                ),
                color = colorResource(R.color.white),
                modifier = Modifier
                    .clickDebounceNoEffect { onClick.invoke() }
            )
            if (isEditing) {
                Icon(
                    painter = painterResource(R.mipmap.ic_delete_history),
                    contentDescription = stringResource(R.string.libs_delete),
                    modifier = Modifier
                        .size(Dp_14)
                        .clickDebounceNoEffect { onDelete() }
                        .padding(start = 4.dp),
                    tint = colorResource(R.color.white)
                )
            }
        }
    }
}
