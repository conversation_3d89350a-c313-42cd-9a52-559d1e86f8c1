@file:Suppress("ForbiddenComment", "MagicNumber")

package com.superhexa.supervision.feature.miwear.speechhub.presentation.recording.helper

import android.content.ContentValues
import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import android.os.Environment
import android.provider.MediaStore
import androidx.annotation.Keep
import com.superhexa.supervision.library.base.basecommon.tools.IntentUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import kotlin.coroutines.CoroutineContext

object ImageShare : CoroutineScope {

    private val baseViewModelJob = SupervisorJob()

    override val coroutineContext: CoroutineContext
        get() = baseViewModelJob + Dispatchers.IO

    private var templeFile: File? = null

    fun share(context: Context, bitmap: Bitmap, fileName: String, onCompleted: (Boolean) -> Unit) {
        launch(Dispatchers.IO) {
            val convertedFile = bitmapToFile(context, bitmap, fileName)
            convertedFile?.let {
                onCompleted(true)
                IntentUtils.shareImage(context, it, fileName)
            } ?: run {
                onCompleted(false)
            }
        }
    }

    // 必须先调用bitmapToFile再使用这个！
    fun share(context: Context, onCompleted: (Boolean) -> Unit) {
        launch(Dispatchers.IO) {
            templeFile?.let {
                onCompleted(true)
                IntentUtils.shareImage(context, it, it.name)
            } ?: run {
                onCompleted(false)
            }
        }
    }

    fun getTempFile() = templeFile
    fun getFilePath(): String = templeFile?.absolutePath ?: ""

    @Suppress("TooGenericExceptionCaught")
    fun bitmapToFile(context: Context, bitmap: Bitmap, fileName: String? = "test.png"): File? {
        return try {
            clearImageCache()
            // 创建一个文件，放在缓存目录里
            templeFile = File(context.cacheDir, "$fileName.png")
            templeFile?.let {
                it.createNewFile()
                // 将 Bitmap 写入文件输出流
                val bos = ByteArrayOutputStream()
                bitmap.compress(Bitmap.CompressFormat.PNG, 100, bos)
                val bitmapData = bos.toByteArray()

                // 将字节写入文件
                FileOutputStream(it).use { fos ->
                    fos.write(bitmapData)
                    fos.flush()
                }
            }
            templeFile
        } catch (e: Exception) {
            Timber.e("bitmapToFile, error -> $e")
            null
        }
    }

    @Suppress("TooGenericExceptionCaught")
    fun saveToGallery(context: Context, fileName: String? = "test.png", onCompleted: (SaveResult) -> Unit) {
        launch(Dispatchers.IO) {
            val file = templeFile
            if (file == null || !file.exists()) {
                withContext(Dispatchers.Main) {
                    onCompleted(SaveResult.FileNotFound)
                }
                return@launch
            }

            try {
                val resolver = context.contentResolver
                val contentValues = ContentValues().apply {
                    put(MediaStore.Images.Media.DISPLAY_NAME, fileName)
                    put(MediaStore.Images.Media.MIME_TYPE, "image/png")
                    put(MediaStore.Images.Media.RELATIVE_PATH, Environment.DIRECTORY_PICTURES)
                    put(MediaStore.Images.Media.IS_PENDING, 1)
                }

                val uri: Uri? = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)
                if (uri != null) {
                    resolver.openOutputStream(uri)?.use { outputStream ->
                        FileInputStream(file).use { inputStream ->
                            inputStream.copyTo(outputStream)
                        }
                    }

                    contentValues.clear()
                    contentValues.put(MediaStore.Images.Media.IS_PENDING, 0)
                    resolver.update(uri, contentValues, null, null)

                    withContext(Dispatchers.Main) {
                        onCompleted(SaveResult.Success)
                    }
                } else {
                    withContext(Dispatchers.Main) {
                        onCompleted(SaveResult.InsertFailed)
                    }
                }
            } catch (e: Exception) {
                Timber.e("saveToGallery, error -> $e")
                withContext(Dispatchers.Main) {
                    onCompleted(SaveResult.ExceptionOccurred(e))
                }
            }
        }
    }

    private fun clearImageCache() {
        // 删除缓存
        templeFile?.delete()
        templeFile = null
    }
}

@Keep
sealed class SaveResult {
    object Success : SaveResult()
    object FileNotFound : SaveResult()
    object InsertFailed : SaveResult()
    data class ExceptionOccurred(val exception: Exception) : SaveResult()
}
