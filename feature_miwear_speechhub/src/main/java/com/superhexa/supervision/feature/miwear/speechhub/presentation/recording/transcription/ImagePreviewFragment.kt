@file:Suppress("MagicNumber")

package com.superhexa.supervision.feature.miwear.speechhub.presentation.recording.transcription

import android.graphics.BitmapFactory
import android.graphics.PointF
import android.os.Bundle
import android.view.View
import androidx.activity.addCallback
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import com.alibaba.android.arouter.facade.annotation.Route
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.miwear.speechhub.R
import com.superhexa.supervision.feature.miwear.speechhub.presentation.recording.helper.ImageShare
import com.superhexa.supervision.feature.miwear.speechhub.presentation.recording.helper.SaveResult
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.Dp_14
import com.superhexa.supervision.library.base.basecommon.theme.Dp_18
import com.superhexa.supervision.library.base.basecommon.theme.Dp_26
import com.superhexa.supervision.library.base.basecommon.theme.Dp_56
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_18
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import com.superhexa.supervision.library.base.subscaleview.ImageSource
import com.superhexa.supervision.library.base.subscaleview.SubsamplingScaleImageView

@Route(path = RouterKey.MIWEAR_IMAGE_PREVIEW_FRAGMENT)
class ImagePreviewFragment : BaseComposeFragment() {

    private var imagePath: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        requireActivity().onBackPressedDispatcher.addCallback(this) { onBackPressedAction() }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initContentView()
    }

    override val contentView: @Composable () -> Unit = {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            val pngFile = remember(imagePath) {
                ImageShare.getTempFile() ?: run {
                    val originBitmap = BitmapFactory.decodeFile(imagePath)
                    ImageShare.bitmapToFile(
                        context = requireContext(),
                        bitmap = originBitmap,
                        fileName = ImageShare.getTempFile()?.name
                    )
                }
            }

            // 顶部标题栏
            TitleBar(title = stringResource(R.string.text_image_preview))

            // 中部：可滚动图片预览
            Row(
                modifier = Modifier
                    .padding(horizontal = 37.42.dp)
                    .fillMaxWidth()
                    .weight(1f)
                    .clipToBounds()
            ) {
                pngFile?.let { file ->
                    val (sWidth, sHeight) = remember { getImageSize(file.absolutePath) }
                    BoxWithConstraints(modifier = Modifier.fillMaxWidth()) {
                        val maxHeightPx = with(LocalDensity.current) { maxHeight.toPx() }
                        val viewWidthPx = constraints.maxWidth.toFloat()
                        val imageWidthPx = sWidth.toFloat()
                        val imageHeightPx = sHeight.toFloat()

                        val scaledHeightPx = imageHeightPx * (viewWidthPx / imageWidthPx)
                        val finalHeightPx = minOf(scaledHeightPx, maxHeightPx)
                        val finalHeightDp = with(LocalDensity.current) { finalHeightPx.toDp() }

                        AndroidView(
                            factory = { context ->
                                SubsamplingScaleImageView(context).apply {
                                    setMinimumTileDpi(160)
                                    isZoomEnabled = false
                                    setDoubleTapZoomDuration(0)
                                    setPanEnabled(true)
                                    setMinimumScaleType(SubsamplingScaleImageView.SCALE_TYPE_START)
                                    setImage(ImageSource.uri(file.path))
                                }
                            },
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(finalHeightDp),
                            update = { view ->
                                view.setOnImageEventListener(object : SubsamplingScaleImageView.OnImageEventListener {
                                    override fun onReady() {
                                        val scale = view.width.toFloat() / imageWidthPx
                                        val centerX = imageWidthPx / 2f
                                        view.setScaleAndCenter(scale, PointF(centerX, 0f))
                                    }
                                    override fun onImageLoaded() = Unit
                                    override fun onPreviewLoadError(e: Exception?) = Unit
                                    override fun onImageLoadError(e: Exception?) = Unit
                                    override fun onTileLoadError(e: Exception?) = Unit
                                    override fun onPreviewReleased() = Unit
                                })
                            }
                        )
                    }
                }
            }

            // 底部按钮栏
            BottomButtonBar(
                onSaveClick = {
                    ImageShare.saveToGallery(
                        context = requireContext(),
                        fileName = pngFile?.name,
                        onCompleted = { saveResult ->
                            when (saveResult) {
                                SaveResult.Success -> {
                                    context.toast(
                                        requireContext().getString(R.string.text_image_save_success)
                                    )
                                }

                                SaveResult.FileNotFound,
                                SaveResult.InsertFailed,
                                is SaveResult.ExceptionOccurred -> {
                                    context.toast(
                                        requireContext().getString(
                                            R.string.text_image_save_failed
                                        ) + saveResult.toString()
                                    )
                                }
                            }
                        }
                    )
                },
                onShareClick = {
                    ImageShare.share(
                        context = requireContext(),
                        onCompleted = { result ->
                            if (!result) {
                                context.toast(
                                    requireContext().getString(R.string.text_image_share_failed)
                                )
                            }
                        }
                    )
                }
            )
        }
    }

    @Composable
    private fun TitleBar(title: String) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(Dp_56),
            contentAlignment = Alignment.Center
        ) {
            // 居中标题
            Text(
                text = title,
                style = TextStyle(
                    fontSize = Sp_18,
                    fontWeight = FontWeight(weight = 380),
                    textAlign = TextAlign.Center
                ),
                color = colorResource(R.color.white),
                maxLines = 1,
                modifier = Modifier.wrapContentSize()
            )

            // 左侧返回按钮
            Image(
                painter = painterResource(id = R.drawable.ic_back_white),
                contentDescription = "返回",
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .padding(start = Dp_18)
                    .size(32.dp)
                    .clickable { onBackPressedAction() }
            )
        }
    }

    @Composable
    fun BottomButtonBar(
        onSaveClick: () -> Unit,
        onShareClick: () -> Unit
    ) {
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .height(106.dp),
            color = colorResource(R.color.color_18191A),
            shape = RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 30.dp),
                horizontalArrangement = Arrangement.spacedBy(9.dp, Alignment.CenterHorizontally),
                verticalAlignment = Alignment.CenterVertically
            ) {
                CustomButton(text = "保存图片", onClick = onSaveClick)
                CustomButton(text = "分享", onClick = onShareClick)
            }
        }
    }

    @Composable
    fun CustomButton(text: String, onClick: () -> Unit) {
        Button(
            onClick = onClick,
            colors = ButtonDefaults.buttonColors(
                backgroundColor = colorResource(R.color.color_222425)
            ),
            shape = RoundedCornerShape(23.dp),
            modifier = Modifier
                .width(153.dp)
                .height(46.dp),
            contentPadding = PaddingValues(horizontal = Dp_26, vertical = Dp_14),
            elevation = null
        ) {
            Text(
                text = text,
                style = TextStyle(
                    fontSize = Sp_13,
                    fontWeight = FontWeight(weight = 380),
                    textAlign = TextAlign.Center
                ),
                color = colorResource(R.color.white),
                maxLines = 1
            )
        }
    }

    private fun onBackPressedAction() {
        navigator.pop()
    }

    private fun initContentView() {
        arguments?.let { bundle ->
            imagePath = bundle.getString(IMAGE_PATH)
        }
    }

    private fun getImageSize(path: String): Pair<Int, Int> {
        val options = BitmapFactory.Options().apply {
            inJustDecodeBounds = true
        }
        BitmapFactory.decodeFile(path, options)
        return options.outWidth to options.outHeight
    }

    companion object {
        const val IMAGE_PATH = "image_path"
    }
}
