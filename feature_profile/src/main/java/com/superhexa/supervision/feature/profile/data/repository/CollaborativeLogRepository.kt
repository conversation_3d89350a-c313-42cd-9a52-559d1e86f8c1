@file:Suppress("TooGenericExceptionCaught")

package com.superhexa.supervision.feature.profile.data.repository

import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.product.ProductManager
import com.superhexa.supervision.feature.profile.data.CollaborativeStatus
import com.superhexa.supervision.feature.profile.data.model.CollaborativeLogResponse
import com.superhexa.supervision.feature.profile.data.retrofit.service.CollaborativeLogService
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import com.superhexa.supervision.library.base.basecommon.tools.AppEnvironment
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.net.retrofit.DataResult
import org.json.JSONObject
import timber.log.Timber

/**
 * 眼镜协同唤醒一键日志上传主体类
 */
class CollaborativeLogRepository {
    private val collaborativeLogService =
        CollaborativeLogFactory.provideService(CollaborativeLogService::class.java)

    companion object {
        const val PROBLEM_TYPE = 680
    }

    /**
     * 调用接口上传日志
     */
    suspend fun uploadCollaborativeLog(
        collaborativeContent: String? = null,
        token: String
    ): DataResult<CollaborativeLogResponse> {
        Timber.d("CollaborativeLogRepository uploadCollaborativeLog")
        return try {
            val userId = AccountManager.getUserID()
            val bondDevice = BlueDeviceDbHelper.getBondDevice()
            val appEnvironment = AppEnvironment(LibBaseApplication.instance)
            val firmwareVersion = MMKVUtils.decodeString(ConstsConfig.FIRMWARE_VERSION)
            val product = bondDevice?.let { ProductManager.getProductByModel(it.miWearModel) }
            val json = JSONObject().apply {
                put("userid", userId.toLong())
                put("problemClass", 1)
                put("problemType", PROBLEM_TYPE)
                put("imeiSha2", bondDevice!!.sn.toString())
                put("miVersion", firmwareVersion.toString())
                put("model", bondDevice.miWearModel)
                put("deviceName", product?.productName.toString())
                put("content", collaborativeContent.toString())
                put("language", "zh_CN")
                put("appName", "米家眼镜")
                put("packageName", LibBaseApplication.instance.packageName)
                put("appVersionName", appEnvironment.getAppVersion())
                put("appVersionCode", appEnvironment.getVersionCode().toString())
                put("platform", 1)
                put("osVersion", appEnvironment.getOSVersion())
                put("wideTagId", 0)
                put("tagId", 0)
                put("tagVersion", 0)
                put("subChannel", "")
                put("occurrenceTime", "")
            }
            Timber.d("uploadCollaborativeLog start")
            val response =
                collaborativeLogService.uploadCollaborativeLog(
                    token = token,
                    data = json.toString()
                )
            Timber.d("uploadCollaborativeLog response: $response")
            if (response.result == CollaborativeStatus.SUCCESS) {
                DataResult.success(response)
            } else {
                DataResult.error("uploadCollaborativeLog fail")
            }
        } catch (e: Exception) {
            DataResult.error(e.toString())
        }
    }
}
