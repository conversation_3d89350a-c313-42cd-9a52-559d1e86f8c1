@file:Suppress("TooGenericExceptionCaught")

package com.superhexa.supervision.feature.xiaoai.presentation.chat

import android.content.ContentValues
import android.os.Environment
import android.provider.MediaStore
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.voice.MiWearVoiceHandler
import com.superhexa.supervision.feature.xiaoai.R
import com.superhexa.supervision.feature.xiaoai.presentation.component.ImageState
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.basecommon.permission.PermissionWrapper
import com.superhexa.supervision.library.base.extension.permissionCheck
import com.superhexa.supervision.library.base.permission.PermissionHint
import com.superhexa.supervision.library.base.presentation.mvi.BaseMVIViewModel
import com.superhexa.supervision.library.base.webviewhelper.WebViewState
import com.superhexa.supervision.library.db.bean.ChatRecord
import com.superhexa.supervision.library.db.bean.MessageType
import com.xiaomi.ai.api.Template
import com.xiaomi.ai.api.common.Instruction
import com.xiaomi.aivs.AiSpeechEngine
import com.xiaomi.aivs.data.StreamType
import com.xiaomi.aivs.engine.event.DeviceEvent
import com.xiaomi.aivs.engine.helper.ImageFileHandler
import com.xiaomi.aivs.engine.helper.ImageTransferListener
import com.xiaomi.aivs.engine.listener.ISpeechChatListener
import com.xiaomi.wearable.context
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import java.io.IOException

class BaseHistoryViewModel :
    BaseMVIViewModel<ChatHistoryState, ChatHistoryEffect, ChatHistoryEvent>(),
    ISpeechChatListener,
    ImageTransferListener {
    private var currentImageId: String? = null

    private var transferImageId: String? = null

    private var historyDbHelper = ChatHistoryDbHelper()

    private val _history = mutableStateListOf<ChatRecord>()
    val history: List<ChatRecord> get() = _history

    private val _records = mutableStateListOf<ChatRecord>()
    val records: List<ChatRecord> get() = _records

    val webViewState = mutableStateOf<WebViewState?>(null)

    private val _imageState = mutableStateOf<ImageState>(ImageState.Initial)
    val imageState: State<ImageState> get() = _imageState

    private val _isImageTransfer = mutableStateOf<Boolean>(false)
    val isImageTransfer: State<Boolean> get() = _isImageTransfer

    private var fallbackTimerJob: Job? = null

    override fun initUiState(): ChatHistoryState = ChatHistoryState()

    @OptIn(DelicateCoroutinesApi::class)
    override fun reduce(oldState: ChatHistoryState, event: ChatHistoryEvent) {
        when (event) {
            is ChatHistoryEvent.LoadChatRecord -> {
                loadChatRecord()
            }

            is ChatHistoryEvent.LoadStreamRecord -> {
                loadStreamRecord(event.sessionId)
            }

            is ChatHistoryEvent.Remove -> {
                Timber.d("remove item:${event.record}")
                event.record?.let { record ->
                    _history.filter { record.dialogId == it.dialogId }.onEach {
                        _history.remove(it)
                    }
                    _records.filter { record.dialogId == it.dialogId }.onEach {
                        _records.remove(it)
                    }
                    GlobalScope.launch {
                        historyDbHelper.removeRecord(record)
                    }
                    if (true == event.needToast) {
                        sendEffect(ChatHistoryEffect.Toast(R.string.tips_delete_success))
                    }
                }
            }
        }
    }

    fun observerAiSpeechChat(lifecycle: Lifecycle) {
        Timber.d("observerAiSpeechChat")
        AiSpeechEngine.INSTANCE.addChatDataObserver(
            key = OBSERVER_NAME,
            lifecycle = lifecycle,
            listener = this
        )
    }

    override fun onQueryRecognize(
        sessionId: String?,
        dialogId: String?,
        query: String?,
        isFinal: Boolean,
        isFromPostImageForLinkImgId: Boolean?,
        instructionJson: String?,
        streamId: String?
    ) {
        Timber.d("onQueryRecognize:$sessionId,$dialogId,$query,$isFinal,$streamId,$instructionJson")
        if (!isFinal || streamId.isNotNullOrEmpty()) return

        query?.takeIf { it.isNotEmpty() && dialogId.isNotNullOrEmpty() }?.let {
            if (isSameDialog(dialogId)) {
                Timber.d("onQueryRecognize update:${_records.lastOrNull()?.imageResponse?.value}")
                _records.lastOrNull()?.let { record ->
                    record.query = query
                    instructionJson?.let { it1 ->
                        record.instructionList?.add(addTimeStampToJsonString(it1))
                    }
                }
            } else {
                Timber.d("onQueryRecognize add:$query,$instructionJson")
                _records.add(
                    ChatRecord(
                        sessionId = sessionId,
                        dialogId = dialogId ?: "",
                        query = query
                    ).apply {
                        instructionJson?.let { it1 ->
                            instructionList?.add(addTimeStampToJsonString(it1))
                        }
                    }
                )
            }
        }
    }

    override fun onParkingCard(
        dialogId: String?,
        sessionId: String?,
        title: String,
        subTitle: String,
        url: String,
        instructionJson: String?
    ) {
        _records.add(
            ChatRecord(
                sessionId = sessionId,
                dialogId = dialogId ?: ""
            ).apply {
                instructionJson?.let {
                    instructionList?.add(it)
                }
            }
        )
    }

    override fun onImageQAContent(
        dialogId: String?,
        sessionId: String?,
        imgInstruction: Instruction<*>?
    ) {
        Timber.d("onImageQAContent:$sessionId,$dialogId,$sessionId,$imgInstruction")
        if (isSameDialog(dialogId)) {
            _records.lastOrNull()?.let { record ->
                val requestId =
                    _records.lastOrNull()?.imageId ?: _records.lastOrNull()?.imageResponse?.value
                Timber.d("onImageQAContent requestId:$requestId")
                record.imageResponse?.value?.let {
                    imgInstruction?.payload?.let { payload ->
                        (payload as Template.ImageQAContent).setHoldImgIds(arrayListOf(it))
                    }
                }
                imgInstruction?.let { img ->
                    record.instructionList?.add(addTimeStampToJsonString(img.toString()))
                }
            }
        }
    }

    override fun onImageQuery(dialogId: String?, requestId: String, instructionJson: String?) {
        Timber.d("onImageQuery:$dialogId,$requestId")
        if (isSameDialog(dialogId)) {
            // imageid更新
            Timber.d("onImageQuery isSameDialog")
            _records.lastOrNull()?.imageResponse?.value = requestId
            _records.lastOrNull()?.imageId = requestId
        }
    }

    override fun onDialogIllegal(sessionId: String?, dialogId: String?) {
        Timber.d("onDialogIllegal:$sessionId,$dialogId")
        MainScope().launch {
            dialogId?.let {
                val record = historyDbHelper.queryUserRecord(dialogId)
                Timber.d("onDialogIllegal:record $record")
                record.forEach {
                    sendEvent(ChatHistoryEvent.Remove(it, false))
                }
            }
        }
    }

    @Suppress("EmptyFunctionBlock")
    override fun addToChatHistory(
        sessionId: String?,
        dialogId: String,
        content: String,
        type: Int,
        timestamp: Long
    ) {
    }

    override fun onTextResponseSynthesizer(
        sessionId: String?,
        dialogId: String?,
        result: String?,
        isFinal: Boolean,
        instructionJson: String?,
        streamId: String?
    ) {
        Timber.d("onTextResponseSynthesizer:$dialogId,$result,$isFinal,$streamId,$instructionJson")
        if (!isFinal || streamId.isNotNullOrEmpty()) return

        result?.takeIf { it.isNotEmpty() && dialogId.isNotNullOrEmpty() }?.let {
            if (isSameDialog(dialogId)) {
                Timber.d("onTextResponseSynthesizer responseContent update")
                _records.lastOrNull()?.responseContent?.value = result
                instructionJson?.let {
                    _records.lastOrNull()?.instructionList?.add(it)
                }
            } else {
                _records.add(
                    ChatRecord(
                        sessionId = sessionId,
                        dialogId = dialogId ?: ""
                    ).apply {
                        responseContent?.value = result
                        instructionJson?.let {
                            instructionList?.add(it)
                        }
                    }
                )
            }
        }
    }

    @Suppress("EmptyFunctionBlock")
    override fun onAlipayTextResponseSynthesizer(
        sessionId: String?,
        dialogId: String?,
        result: String?,
        isFinal: Boolean,
        instructionJson: String?,
        streamId: String?
    ) {
    }

    override fun onResponseBottomExplain(
        sessionId: String?,
        dialogId: String?,
        bottomExplain: String?,
        instructionJson: String?
    ) {
        Timber.d("onResponseBottomExplain:$dialogId,$bottomExplain,$instructionJson")
        bottomExplain?.takeIf { it.isNotEmpty() && dialogId.isNotNullOrEmpty() }?.let {
            if (isSameDialog(dialogId)) {
                Timber.d("onResponseSynthesizer bottomExplain update $it")
                _records.lastOrNull()?.bottomExplain = it
                instructionJson?.let {
                    _records.lastOrNull()?.instructionList?.add(it)
                }
            } else {
                _records.add(
                    ChatRecord(
                        sessionId = sessionId,
                        dialogId = dialogId ?: "",
                        bottomExplain = bottomExplain
                    ).apply {
                        instructionJson?.let {
                            instructionList?.add(it)
                        }
                    }
                )
            }
        }
    }

    override fun onStreamDialogEnter(
        sessionId: String?,
        dialogId: String?,
        streamType: String,
        streamId: String,
        cardConfig: Triple<String, String, String>?,
        pageConfig: Triple<String, String, String?>?,
        instructionJson: String?
    ) {
        Timber.d("onStreamDialogEnter:$dialogId,$sessionId,$streamId,$cardConfig,$pageConfig")
        dialogId?.let {
            _records.add(
                ChatRecord(
                    messageType = MessageType.STREAM_CARD,
                    sessionId = sessionId,
                    dialogId = dialogId,
                    title = cardConfig?.first
                        ?: context.getString(R.string.title_message_standby),
                    description = cardConfig?.second
                        ?: context.getString(R.string.tip_standby),
                    iconUrl = if (streamType != StreamType.STANDBY) cardConfig?.third else ""
                ).apply {
                    instructionJson?.let { it1 -> instructionList?.add(it1) }
                }
            )
        }
    }

    private fun loadChatRecord() = viewModelScope.launch(Dispatchers.IO) {
        val pageNo = if (_history.isEmpty()) {
            DEFAULT_PAGE_NO
        } else {
            (_history.size + PAGE_SIZE - 1) / PAGE_SIZE + 1
        }
        val data = historyDbHelper.queryUserRecord(pageNo, pageSize = PAGE_SIZE)
        _history.addAll(0, data)
        Timber.d("loadRecordList:$pageNo，${data.size},${_history.size}")
        delay(DELAY_TIME)
        setState(mState.value.copy(pageNo = pageNo))
    }

    @Suppress("TooGenericExceptionCaught")
    fun removeRecord(dialogId: String?) {
        viewModelScope.launch(Dispatchers.IO) {
            dialogId?.let { id ->
                try {
                    val records = historyDbHelper.queryUserRecord(id)
                    Timber.d("removeRecord: found ${records.size} records for dialogId $id")
                    if (records.isNotEmpty()) {
                        for (record in records) {
                            historyDbHelper.deleteItem(record)
                        }
                        Timber.i("removeRecord: deleted ${records.size} records for dialogId $id")
                    } else {
                        Timber.w("removeRecord: no records found for dialogId $id")
                    }
                } catch (e: Exception) {
                    Timber.e(e, "removeRecord: failed to delete records for dialogId $id")
                }
            }
        }
    }

    fun queryAndRemoveRecord(dialogId: String?) {
        viewModelScope.launch(Dispatchers.IO) {
            dialogId?.let { id ->
                Timber.d("removeRecord:records $id")
                // 安全访问：使用 firstOrNull() 替代 first()，避免空列表崩溃
                val record = historyDbHelper.queryUserRecord(id)?.firstOrNull()
                record?.let { sendEvent(ChatHistoryEvent.Remove(it)) }
                    ?: Timber.w("No record found for dialogId: $id") // 可选：记录未找到记录的情况
            }
        }
    }

    private fun loadStreamRecord(sessionId: String) = viewModelScope.launch(Dispatchers.IO) {
        _history.addAll(historyDbHelper.queryAllStreamRecord(sessionId))
        Timber.d("loadStreamRecord:$sessionId，${_history.size}")
        delay(DELAY_TIME)
    }

    fun hideLoading() {
        Timber.d("hideLoading")
        setState(mState.value.copy(isLoading = false))
    }

    private fun isSameDialog(dialogId: String?): Boolean {
        return _records.lastOrNull()?.dialogId == dialogId
    }

    private fun addTimeStampToJsonString(instruction: String?): String {
        Timber.d("addTimeStampToJsonString:$instruction")
        if (instruction.isNullOrBlank() || instruction == "null") {
            Timber.w("Invalid instruction JSON: $instruction")
            return "{}" // 返回安全空对象
        }
        // 获取当前时间戳
        val timestamp = System.currentTimeMillis()
        // 将 instruction 转换为 JSON 字符串（假设 toString() 已经返回 JSON 格式）
        val instructionJsonString = instruction ?: return ""
        // 使用 Gson 解析 JSON 字符串
        val json = Gson().fromJson(instructionJsonString, JsonObject::class.java)
        // 确保 header 存在并为 JsonObject
        if (json.has("header") && json.get("header").isJsonObject) {
            val headerJson = json.getAsJsonObject("header")
            headerJson.addProperty("timestamp", timestamp)
        }
        return json.toString()
    }

    fun saveToGallery(fragment: Fragment, file: File) {
        fragment.permissionCheck(
            grantAction = { all, permissions ->
                Timber.i("saveToGallery all %s permissions %s", all, permissions)
                if (all) {
                    saveToGallery(file)
                }
            },
            deniedAction = { never, permissions, lastNormalDenyAll ->
                Timber.i("选择媒体页面 never %s permissions %s", never, permissions)
                if (never && !lastNormalDenyAll) {
                    sendEffect(ChatHistoryEffect.Toast(R.string.denyForeverPlsAllow))
                    // 如果是被永久拒绝先弹框提示
                    PermissionHint.hintForNeverAllowPermission(
                        fragment,
                        permissions,
                        title = fragment.getString(R.string.hintStoragePermission),
                        confirmText = fragment.getString(R.string.authorize)
                    )
                } else {
                    sendEffect(ChatHistoryEffect.Toast(R.string.acquireWriteSDcardFailed))
                }
            },
            com.superhexa.supervision.library.base.R.string.permissionStorageDesc,
            PermissionWrapper.EXTERNAL_STORAGE
        )
    }

    private fun saveToGallery(file: File) {
        Timber.i("SaveToGallery: ${file.exists()}")
        if (file.exists()) {
            try {
                Timber.i("save to the ${MediaStore.Images.Media.EXTERNAL_CONTENT_URI}")
                Timber.i("save to the name ${file.name}")
                val resolver = context.contentResolver
                val contentValues = ContentValues().apply {
                    put(MediaStore.Images.Media.DISPLAY_NAME, file.name)
                    put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg")
                    put(MediaStore.Images.Media.TITLE, file.name)
                    put(MediaStore.Images.Media.DESCRIPTION, "Image from chat")
                    // 对于 Android Q+，需要指定相对路径
                    put(
                        MediaStore.Images.Media.RELATIVE_PATH,
                        Environment.DIRECTORY_PICTURES
                    )
                    put(MediaStore.Images.Media.IS_PENDING, 1)
                }

                val uri = resolver.insert(
                    MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                    contentValues
                ) ?: throw IOException("Failed to create new MediaStore record")

                resolver.openOutputStream(uri).use { outputStream ->
                    if (outputStream == null) {
                        throw IOException("Failed to get output stream")
                    }

                    file.inputStream().use { inputStream ->
                        try {
                            inputStream.copyTo(outputStream)
                            // 对于 Android Q+, 需要设置 IS_PENDING 为 0 表示写入完成
                            contentValues.clear()
                            contentValues.put(MediaStore.Images.Media.IS_PENDING, 0)
                            resolver.update(uri, contentValues, null, null)
                            sendEffect(ChatHistoryEffect.Toast(R.string.tips_file_save_success))
                        } catch (e: IOException) {
                            // 检查是否是存储空间不足
                            if (e.message?.contains("No space left") == true) {
                                sendEffect(ChatHistoryEffect.Toast(R.string.fileSpaceNotEnough))
                            } else {
                                throw e
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                Timber.i("SaveToGallery: Failed to save image to gallery $e")
                sendEffect(ChatHistoryEffect.Toast(R.string.libs_save_failed))
            }
        }
    }

    // 获取大图
    fun fetchLargePicture(imageId: String?, dialogId: String?) {
        Timber.i("fetchLargePicture $imageId $dialogId")
        currentImageId = imageId
        if (isImageTransfer.value) {
            localLoading(imageId)
            return
        }
        transferImageId = imageId
        imageId?.let {
            viewModelScope.launch(Dispatchers.IO) {
                val image = getImage(it, ImageFileHandler.ImageType.ORIGINAL)
                if (null == image) {
                    fallbackTimerJob?.cancel()
                    MiWearVoiceHandler.INSTANCE.registerImageListener(it, this@BaseHistoryViewModel)
                    onImageStateChange(true)
                    AiSpeechEngine.INSTANCE.sendEventToDevice(
                        imageId,
                        dialogId,
                        DeviceEvent.LARGE_IMAGE_TRANS_START
                    )
                    // 1. 先尝试获取裁切图
                    var previewImage = getImage(it, ImageFileHandler.ImageType.CROPPED)
                    if (null == previewImage) {
                        previewImage = getImage(it, ImageFileHandler.ImageType.UNKNOWN)
                    }
                    Timber.i("previewImage $previewImage")
                    // 2. 如果有缩略图，先显示缩略图
                    previewImage?.let { file ->
                        try {
                            _imageState.value = ImageState.LoadingWithPreview(file)
                        } catch (e: Exception) {
                            onImageTransferFailed(currentImageId, e.message ?: "未知错误")
                        }
                    }
                    // 请求时间过长先显示缩略图
                    fallbackTimerJob = launch {
                        delay(TIME_OUT)
                        onImageStateChange(false)
                        if (_imageState.value is ImageState.LoadingWithPreview) {
                            previewImage?.let { file ->
                                try {
                                    _imageState.value = ImageState.Success(file)
                                    cleanup()
                                } catch (e: Exception) {
                                    onImageTransferFailed(currentImageId, e.message ?: "未知错误")
                                }
                            }
                        }
                    }
                    // 设置超时
                    delay(MINIMUM_TIME_OUT)
                    if (_imageState.value is ImageState.Loading) {
                        onImageTransferFailed(it, "请求超时")
                    }
                } else {
                    Timber.i("ORIGINAL Image exist")
                    onImageTransferComplete(it, image)
                }
            }
        }
    }

    private suspend fun getImage(imageId: String?, type: ImageFileHandler.ImageType): File? =
        withContext(Dispatchers.IO) {
            Timber.i("getImage $imageId,$type")
            requireNotNull(imageId) { "imageId cannot be null" }

            val imageFile = ImageFileHandler.getImageFile(
                context = context,
                imageId = imageId,
                type
            )
            imageFile?.takeIf { it.exists() }
        }

    // 重置状态
    fun resetImageState() {
        _imageState.value = ImageState.Initial
        cleanup()
    }

    // 清除当前图片并取消监听
    private fun cleanup() {
        currentImageId?.let {
            MiWearVoiceHandler.INSTANCE.unregisterImageListener(it)
            currentImageId = null
        }
    }

    override fun onImageTransferStart(requestId: String) {
        Timber.i("onImageTransferStart $requestId")
        // 点击原图后会取消ViewModel带缩略图的加载效果
//        if (requestId == currentImageId) {
//            _imageState.value = ImageState.Loading
//        }
    }

    override fun onImageDataReceived(requestId: String, progress: Float) {
        Timber.i("onImageDataReceived $requestId")
    }

    // 发送命令传图完成
    override fun onImageTransferComplete(requestId: String) {
        Timber.i("onImageTransferComplete $requestId ,$currentImageId")
        if (requestId == currentImageId) {
            viewModelScope.launch {
                try {
                    val highResFile = getImage(currentImageId, ImageFileHandler.ImageType.ORIGINAL)
                    highResFile?.let {
                        _imageState.value = ImageState.Success(highResFile)
                        AiSpeechEngine.INSTANCE.sendEventToDevice(
                            requestId,
                            _records.lastOrNull()?.dialogId,
                            DeviceEvent.LARGE_IMAGE_TRANS_STOP
                        )
                    }
                    cleanup()
                } catch (e: Exception) {
                    onImageTransferFailed(currentImageId, e.message ?: "未知错误")
                }
            }
        }
    }

    override fun onImageTransferFailed(requestId: String?, error: String) {
        Timber.i("onImageTransferFailed $requestId $error")
        if (requestId == currentImageId) {
            _imageState.value = ImageState.Error(error)
            cleanup()
        }
    }

    override fun onImageStateChange(state: Boolean) {
        Timber.d("onImageStateChange: $state")
        _isImageTransfer.value = state
        if (!state) transferImageId = null
    }

    // 传图完成回调
    override fun onImageTransferComplete(requestId: String, imgFile: File?) {
        Timber.i("onImageTransferComplete $requestId ,$currentImageId")
        if (requestId == currentImageId) {
            viewModelScope.launch {
                try {
                    imgFile?.let {
                        _imageState.value = ImageState.Success(it)
                        AiSpeechEngine.INSTANCE.sendEventToDevice(
                            requestId,
                            _records.lastOrNull()?.dialogId,
                            DeviceEvent.LARGE_IMAGE_TRANS_STOP
                        )
                    }
                    cleanup()
                } catch (e: Exception) {
                    onImageTransferFailed(currentImageId, e.message ?: "未知错误")
                }
            }
        }
    }

    private fun localLoading(imageId: String?) {
        imageId?.let {
            viewModelScope.launch(Dispatchers.IO) {
                MiWearVoiceHandler.INSTANCE.registerImageListener(it, this@BaseHistoryViewModel)
                val image = getImage(it, ImageFileHandler.ImageType.ORIGINAL)
                if (null == image || transferImageId == imageId) {
                    var previewImage = getImage(it, ImageFileHandler.ImageType.CROPPED)
                    if (null == previewImage) {
                        previewImage = getImage(it, ImageFileHandler.ImageType.UNKNOWN)
                    }
                    Timber.i("previewImage $previewImage")
                    previewImage?.let { file ->
                        try {
                            _imageState.value = ImageState.LoadingWithPreview(file)
                        } catch (e: Exception) {
                            onImageTransferFailed(currentImageId, e.message ?: "未知错误")
                        }
                    }
                } else {
                    Timber.i("ORIGINAL Image exist")
                    viewModelScope.launch {
                        try {
                            image.let {
                                _imageState.value = ImageState.Success(it)
                            }
                        } catch (e: Exception) {
                            onImageTransferFailed(currentImageId, e.message ?: "未知错误")
                        }
                    }
                }
            }
        }
    }

    companion object {
        const val PAGE_SIZE = 100L
        const val DELAY_TIME = 260L
        const val TIME_OUT = 12_000L
        const val MINIMUM_TIME_OUT = 300_000L
        private const val OBSERVER_NAME = "ChatHistory"
    }
}
