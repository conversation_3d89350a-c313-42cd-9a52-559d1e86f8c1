package com.superhexa.supervision.feature.xiaoai.service

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleService
import androidx.lifecycle.Observer
import com.jeremyliao.liveeventbus.LiveEventBus
import com.superhexa.supervision.feature.xiaoai.data.IntentConst.ACTION_CONTINUOUS_DIALOG_ENTER
import com.superhexa.supervision.feature.xiaoai.data.IntentConst.ACTION_DEVICE_STATE_CONNECT
import com.superhexa.supervision.feature.xiaoai.data.IntentConst.ACTION_DEVICE_STATE_DISCONNECT
import com.superhexa.supervision.feature.xiaoai.data.IntentConst.ACTION_DIALOG_EXIT
import com.superhexa.supervision.feature.xiaoai.data.IntentConst.ACTION_STREAM_ENTER
import com.superhexa.supervision.feature.xiaoai.data.IntentConst.ACTION_STREAM_HANG_UP
import com.superhexa.supervision.feature.xiaoai.presentation.observer.DeviceObserver
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.AlertStatus
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.DeviceWearStatus
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.IsTranslate
import com.superhexa.supervision.library.base.basecommon.tools.FeedBackUtil
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.db.bean.bluedevice.BondDevice
import com.xiaomi.aivs.AiSpeechEngine
import com.xiaomi.aivs.data.DialogState
import timber.log.Timber

class AiSpeechService : LifecycleService() {

    private val stateObserver = DeviceObserver()
    private var speechReceiver: SpeechReceiver? = null
    private var curAction: String? = null
    private var isWear = false
    private var recorderStatus = 0
    private var isTranslate = false

    override fun onCreate() {
        super.onCreate()
        Timber.d("onCreate")
        MiLiteComponent.init(this, this)
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Timber.d("onStartCommand")
        startUpAivsForeground(lifecycleOwner = this)
        return super.onStartCommand(intent, flags, startId)
    }

    private fun startUpAivsForeground(lifecycleOwner: LifecycleOwner) {
        val newDevice = stateObserver.initObserver(lifecycleOwner) { connect, device ->
            onDeviceConnectState(connect, device)
        }
        onDeviceConnectState(isDeviceConnected(), newDevice)
    }

    private fun onDeviceConnectState(connected: Boolean, device: BondDevice?) {
        Timber.d("onDeviceConnectState:$connected,$device")
        if (connected) {
            registerReceiver()
            startForegroundByAction(ACTION_DEVICE_STATE_CONNECT)
            device?.let { MiLiteComponent.startUp(this, device, this) }
            FeedBackUtil.ifNeedStartFeedBackWorker(this)
            FeedBackUtil.ifNeedStartSceneLogWorker(this)
            FeedBackUtil.startUploadRawImageWorker(this)
        } else {
            unregisterReceiver()
            startForegroundByAction(ACTION_DEVICE_STATE_DISCONNECT)
            MiLiteComponent.release(withObserver = false)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Timber.d("onDestroy")
        unregisterReceiver()
        MiLiteComponent.release()
    }

    private fun isDeviceConnected() = stateObserver.isDeviceConnected()

    private fun registerReceiver() {
        Timber.d("registerReceiver")
        speechReceiver = SpeechReceiver()
        val intentFilter = IntentFilter()
        intentFilter.addAction(ACTION_CONTINUOUS_DIALOG_ENTER)
        intentFilter.addAction(ACTION_STREAM_ENTER)
        intentFilter.addAction(ACTION_DIALOG_EXIT)
        intentFilter.addAction(ACTION_STREAM_HANG_UP)
        speechReceiver?.let {
            MiLiteHelper.registerReceiver(this, it, intentFilter)
        }

        LiveEventBus.get(DeviceWearStatus, Boolean::class.java)
            .observe(
                this,
                Observer { data ->
                    Timber.d("get deviceWearStatus--get=%s", data)
                    isWear = data
                    MiLiteHelper.startForegroundForIdle(
                        this@AiSpeechService,
                        isDeviceConnected(),
                        isWear,
                        recorderStatus,
                        isTranslate
                    )
                }
            )

        LiveEventBus.get(AlertStatus, Int::class.java)
            .observe(
                this,
                Observer { data ->
                    Timber.d("get alertStatus--get=%s", data)
                    recorderStatus = data

                    // 接收并保存status
                    MMKVUtils.encode(AlertStatus, recorderStatus)

                    MiLiteHelper.startForegroundForIdle(
                        this@AiSpeechService,
                        isDeviceConnected(),
                        isWear,
                        recorderStatus,
                        isTranslate
                    )
                }
            )
        LiveEventBus.get(IsTranslate, Boolean::class.java)
            .observe(
                this,
                Observer { data ->
                    Timber.d("get isTranslate--get=%s", data)
                    isTranslate = data
                    MiLiteHelper.startForegroundForIdle(
                        this@AiSpeechService,
                        isDeviceConnected(),
                        isWear,
                        recorderStatus,
                        isTranslate
                    )
                }
            )
    }

    private fun unregisterReceiver() {
        speechReceiver?.let {
            Timber.d("unregisterReceiver")
            unregisterReceiver(it)
            speechReceiver = null
        }
    }

    private fun startForeground(intent: Intent?) {
        if (AiSpeechEngine.INSTANCE.dialogState() == DialogState.STANDBY) {
            MiLiteHelper.startForegroundForStream(this@AiSpeechService, intent)
        } else if (AiSpeechEngine.INSTANCE.dialogState() == DialogState.RECORDING) {
            MiLiteHelper.startForegroundForContinuousDialog(this@AiSpeechService)
        } else {
            MiLiteHelper.startForegroundForIdle(
                this@AiSpeechService,
                isDeviceConnected(),
                isWear,
                recorderStatus,
                isTranslate
            )
        }
    }

    inner class SpeechReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            Timber.d("onReceive:$intent")
            startForegroundByAction(intent?.action, intent)
        }
    }

    private fun startForegroundByAction(action: String?, intent: Intent? = null) {
        Timber.d("startForegroundByAction:$action,$curAction")

        action?.takeIf { it != curAction }?.let {
            this.curAction = action
            when (action) {
                ACTION_STREAM_ENTER ->
                    MiLiteHelper.startForegroundForStream(this@AiSpeechService, intent)

                ACTION_CONTINUOUS_DIALOG_ENTER ->
                    MiLiteHelper.startForegroundForContinuousDialog(this@AiSpeechService)

                ACTION_DIALOG_EXIT,
                ACTION_DEVICE_STATE_CONNECT,
                ACTION_DEVICE_STATE_DISCONNECT ->
                    MiLiteHelper.startForegroundForIdle(
                        this@AiSpeechService,
                        isDeviceConnected(),
                        isWear,
                        recorderStatus,
                        isTranslate
                    )

                ACTION_STREAM_HANG_UP -> {
                    AiSpeechEngine.INSTANCE.exitStandby(reason = "Notification")
                }
            }
        } ?: run {
            Timber.w("it is inValid action.")
        }
    }
}
