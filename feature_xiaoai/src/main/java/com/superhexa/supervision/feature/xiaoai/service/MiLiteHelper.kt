package com.superhexa.supervision.feature.xiaoai.service

import android.app.PendingIntent
import android.app.Service
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.ServiceInfo
import android.os.Build
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.record.AlertStatus
import com.superhexa.supervision.feature.xiaoai.R
import com.superhexa.supervision.feature.xiaoai.data.IntentConst
import com.superhexa.supervision.feature.xiaoai.data.Keys
import com.superhexa.supervision.feature.xiaoai.presentation.streamDialog.StreamActivity
import com.superhexa.supervision.feature.xiaoai.utils.NotificationData
import com.superhexa.supervision.feature.xiaoai.utils.NotificationHelper
import com.superhexa.supervision.library.base.basecommon.tools.DeviceStateHelper
import com.xiaomi.aivs.AiSpeechEngine
import com.xiaomi.aivs.data.StreamType
import timber.log.Timber

object MiLiteHelper {

    private const val CHANNEL = "AiSpeechService"
    private const val STREAM_CHANNEL = "StreamService"
    private const val CHANNEL_FOR_MULTI_DEVICE = "InhibitionNotificationService"
    private const val NOTIFICATION_ID = 0x111
    private const val NOTIFICATION_ID_FOR_MULTI_DEVICE = 0x222
    private var isWear = false
    private var recorderStatus = 0
    private var isTranslate = false

    const val GLASSES_WAKE_INHIBITION = "GlassesWakeInhibition"
    const val GLASSES_WAKE_INHIBITION_VALUE = 1

    private var streamConfig: Triple<String, String, String?>? = null
    private var welcomeConfig: Pair<String?, String?>? = null

    fun onDialogEnter(context: Context) {
        Timber.d("onDialogEnter")
        context.sendBroadcast(Intent(IntentConst.ACTION_CONTINUOUS_DIALOG_ENTER))
    }

    @Suppress("LongParameterList")
    fun onStreamEnter(
        context: Context,
        notifyTitle: String,
        pageTitle: String,
        icon: String,
        avatar: String?,
        welcome: Pair<String?, String?>?
    ) {
        if (AiSpeechEngine.INSTANCE.getAlipayStatus()) {
            Timber.d("is AlipayStatus")
            return
        }
        this.streamConfig = Triple(pageTitle, icon, avatar)
        this.welcomeConfig = welcome
        val isAppInForeground = DeviceStateHelper.isAppInForeground(context)
        Timber.d(
            "onStreamEnter isAppInForeground" +
                ":$isAppInForeground,$notifyTitle,$pageTitle,$icon,$avatar"
        )
        if (isAppInForeground) {
            launchStandbyActivity(context)
        }
        val intent = Intent(IntentConst.ACTION_STREAM_ENTER).apply {
            putExtra(Keys.TITLE, notifyTitle)
            putExtra(Keys.ICON, icon)
        }
        context.sendBroadcast(intent)
    }

    fun onDialogExit(context: Context) {
        Timber.d("onDialogExit")
        context.sendBroadcast(Intent(IntentConst.ACTION_DIALOG_EXIT))
    }

    fun launchStandbyActivity(context: Context) {
        Timber.d("launchStandbyActivity:$welcomeConfig")
        kotlin.runCatching {
            val intent = Intent(context, StreamActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP
                putExtra(Keys.TITLE, streamConfig?.first ?: "")
                putExtra(Keys.AVATAR, streamConfig?.third ?: "")
                welcomeConfig?.let {
                    putExtra(Keys.DIALOG_ID, it.first)
                    putExtra(Keys.WELCOME, it.second)
                }
            }
            context.startActivity(intent)
        }
    }

    fun startForegroundForStream(context: Service, intent: Intent?) {
        Timber.d("startForegroundForStream")
        startForegroundImpl(
            context = context,
            streamType = intent?.getStringExtra(Keys.STREAM_TYPE) ?: StreamType.STANDBY,
            title = intent?.getStringExtra(Keys.TITLE) ?: context.getString(R.string.title_standby),
            content = context.getString(R.string.tip_standby_notification),
            iconUrl = intent?.getStringExtra(Keys.ICON),
            supportAction = true
        )
    }

    fun startForegroundForContinuousDialog(context: Service) {
        Timber.d("startForegroundForContinuousDialog")
        startForegroundImpl(
            context = context,
            streamType = "DialogState.RECORDING",
            title = context.getString(R.string.app_name),
            content = context.getString(R.string.tip_standby_notification),
            iconId = -1
        )
    }

    fun startForegroundForIdle(
        context: Service,
        isConnected: Boolean,
        isWear: Boolean,
        status: Int,
        isTranslate: Boolean
    ) {
        Timber.d("startForegroundForIdle")
        updateDeviceStatus(isWear, isTranslate, status)
        val content = if (isConnected) {
            if (isWear) {
                if (status == AlertStatus.RECORD_CALLING) { // 通话中，MIC被占用时
                    context.getString(R.string.tip_notification_mic_is_occupied)
                } else if (status == AlertStatus.ON_SITE_RECORDING) { // 开始录音
                    context.getString(R.string.tip_notification_recording_audio_content)
                } else if (status == AlertStatus.RECORD_VIDEO) { // 开始录相
                    context.getString(R.string.tip_notification_recording_video_content)
                } else if (status == AlertStatus.RECORD_OTHERS) {
                    if (isTranslate) { // 开始同声传译
                        context.getString(R.string.tip_notification_start_translate_hub)
                    } else {
                        context.getString(R.string.tip_notification_camera_joint)
                    }
                } else { // 已佩戴，未录音，未唤醒小爱，未在会议中
                    context.getString(R.string.tip_notification_connected_wakeup_xiaoai)
                }
            } else {
                context.getString(R.string.tip_notification_connected)
            }
        } else {
            context.getString(R.string.tip_notification_dis_connected)
        }
        val title = if (isConnected) {
            if (isWear) {
                if (status == AlertStatus.ON_SITE_RECORDING) { // 开始录音
                    context.getString(R.string.tip_notification_recording_audio_title)
                } else if (status == AlertStatus.RECORD_VIDEO) { // 开始录相
                    context.getString(R.string.tip_notification_recording_video_title)
                } else {
                    context.getString(R.string.app_name)
                }
            } else {
                context.getString(R.string.app_name)
            }
        } else {
            context.getString(R.string.app_name)
        }
        startForegroundImpl(
            context = context,
            title = title,
            content = content,
            iconId = -1
        )
    }

    fun startForegroundForGlassesWakeInhibition(context: Service) {
        Timber.d("startForegroundForGlassesWakeInhibition")
        startForegroundImpl(
            context = context,
            streamType = GLASSES_WAKE_INHIBITION,
            title = context.getString(R.string.tip_notification_glasses_wake_inhibition_title),
            content = context.getString(R.string.tip_notification_glasses_wake_inhibition_content),
            iconId = -1
        )
    }

    @Suppress("LongParameterList")
    private fun startForegroundImpl(
        context: Service,
        streamType: String = "",
        title: String,
        content: String,
        iconId: Int = R.mipmap.ic_standby_notification,
        iconUrl: String? = null,
        supportAction: Boolean = false
    ) {
        Timber.d("startForegroundImpl:$streamType,$title,$content,$iconId,$iconUrl,$supportAction")
        var notificationId = if (GLASSES_WAKE_INHIBITION == streamType) {
            NOTIFICATION_ID_FOR_MULTI_DEVICE
        } else {
            NOTIFICATION_ID
        }
        NotificationHelper.clearNotification(context, notificationId)
        NotificationHelper.showForegroundNotification(
            context = context,
            notificationData = NotificationData(
                title = title,
                content = content,
                iconId = iconId,
                iconUrl = iconUrl,
                supportAction = supportAction,
                channelId = if (GLASSES_WAKE_INHIBITION == streamType) {
                    CHANNEL_FOR_MULTI_DEVICE
                } else {
                    CHANNEL
                },
                notificationId,
                foregroundServiceType = ServiceInfo.FOREGROUND_SERVICE_TYPE_MEDIA_PLAYBACK,
                clickPendingIntent = if (supportAction) {
                    notifyPendingIntent(context)
                } else {
                    launchPendingIntent(context, streamType)
                },
                actionPendingIntent = actionPendingIntent(context)
            )
        )
    }

    private fun notifyPendingIntent(context: Context): PendingIntent {
        val notifyIntent = Intent(context, StreamActivity::class.java)
            .apply { flags = Intent.FLAG_ACTIVITY_MULTIPLE_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP }
        return PendingIntent.getActivity(
            context,
            0,
            notifyIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    }

    private fun launchPendingIntent(context: Context, streamType: String?): PendingIntent {
        val notifyIntent = context.packageManager.getLaunchIntentForPackage(context.packageName)
        if (GLASSES_WAKE_INHIBITION == streamType) {
            notifyIntent?.putExtra(GLASSES_WAKE_INHIBITION, GLASSES_WAKE_INHIBITION_VALUE)
        }
        return PendingIntent.getActivity(
            context,
            0,
            notifyIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    }

    private fun actionPendingIntent(context: Context): PendingIntent {
        val intent = Intent(IntentConst.ACTION_STREAM_HANG_UP)
        return PendingIntent.getBroadcast(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    }

    fun registerReceiver(
        context: Context,
        broadcastReceiver: BroadcastReceiver,
        intentFilter: IntentFilter
    ) {
        kotlin.runCatching {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                context.registerReceiver(
                    broadcastReceiver,
                    intentFilter,
                    Context.RECEIVER_NOT_EXPORTED
                )
            } else {
                context.registerReceiver(broadcastReceiver, intentFilter)
            }
        }
    }

    private fun updateDeviceStatus(isWear: Boolean, isTranslate: Boolean, recorderStatus: Int) {
        Timber.i(
            "updateDeviceStatus isWear $isWear,isTranslate $isTranslate," +
                "recorderStatus $recorderStatus"
        )
        this.isWear = isWear
        this.isTranslate = isTranslate
        this.recorderStatus = recorderStatus
    }

    fun getIsWearing(): Boolean {
        Timber.i("getIsWearing $isWear")
        return isWear
    }

    fun getRecorderStatus(): Int {
        Timber.i("getRecorderStatus $recorderStatus")
        return recorderStatus
    }

    fun getIsTranslating(): Boolean {
        Timber.i("getIsTranslating $isTranslate")
        return isTranslate
    }
}
