def appVersionMajor = 2
def appVersionMinor = 0
def appVersionBuild = 219
def globalVersionMajor = 0
def globalVersionMinor = 5
def globalVersionBuild = 105
def appvCode = 2282
def globalvCode = 1289
android {
    flavorDimensions("app", "channel")
    productFlavors {
        xiaomi {
            dimension "channel"
            applicationId 'com.xiaomi.superhexa'
            buildConfigField "String", "MIPUSH_APP_ID", string("\"2882303761520170161\"")
            buildConfigField "String", "MIPUSH_APP_KEY", string("\"5532017090161\"")
            manifestPlaceholders = [SCHEME: "miGlasses"]
        }
        hexa {
            dimension "channel"
            buildConfigField "String", "MIPUSH_APP_ID", string("\"2882303761519967825\"")
            buildConfigField "String", "MIPUSH_APP_KEY", string("\"5541996774825\"")
            manifestPlaceholders = [SCHEME: "superhexa"]
        }
        app {
            dimension "app"
            resConfigs 'zh-rCN'
            versionCode appvCode
            versionName "${appVersionMajor}.${appVersionMinor}.${appVersionBuild}"
        }
        global {
            dimension "app"
            applicationId 'com.superhexa.headwear'
            resConfigs 'en_US', 'es_ES', 'de_DE', 'fr_FR', 'pl_PL', 'zh-rCN'
            versionCode globalvCode
            versionName "${globalVersionMajor}.${globalVersionMinor}.${globalVersionBuild}"
        }
    }
    variantFilter { variant ->
        def names = variant.flavors*.name
        System.out.println("----------names= ${names}")
        makeHexaAppInfo(project, names)
        // 先排除小米渠道，提交到小米后需要开发人员根据需求修改下
//        if (names.contains("dvt") && names.contains("app")) {
//            setIgnore(true)
//        }
    }
}

static def makeHexaAppInfo(Project project, ArrayList<String> flavor) {
    new File(project.projectDir, "/src/main/assets/hexaappinfo.json").withWriter('UTF-8') { writer ->
        writer.write("{")
        if (flavor.contains("app")) {
            writer.write("\"rootDirName\" : \"MiGlasses\"")
        } else {
            writer.write("\"rootDirName\" : \"SUPERHEXA\"")
        }
        writer.write("}")
    }
}

static def string(String value) {
    return "${value}"
}
