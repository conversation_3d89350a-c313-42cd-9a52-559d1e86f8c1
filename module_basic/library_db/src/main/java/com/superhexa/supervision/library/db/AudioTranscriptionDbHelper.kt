@file:Suppress("TooGenericExceptionCaught", "TooManyFunctions", "MaxLineLength")

package com.superhexa.supervision.library.db

import android.annotation.SuppressLint
import com.superhexa.supervision.library.db.bean.AudioTranscriptionBean
import com.superhexa.supervision.library.db.bean.AudioTranscriptionBean_
import com.superhexa.supervision.library.db.bean.MediaBean
import com.superhexa.supervision.library.db.bean.MediaBean_
import com.superhexa.supervision.library.db.bean.SummaryStatus
import com.superhexa.supervision.library.db.bean.TranscriptionStatus
import io.objectbox.BoxStore
import io.objectbox.kotlin.equal
import io.objectbox.reactive.DataSubscription
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber

object AudioTranscriptionDbHelper {
    private val boxStore: BoxStore = DbHelper.getBoxStore()
    private val boxFor by lazy { boxStore.boxFor(AudioTranscriptionBean::class.java) }

    /**
     * 更新是否首次展示小红点
     */
    @SuppressLint("SetWorldWritable")
    suspend fun updateIsFirstShow(bean: AudioTranscriptionBean) = withContext(Dispatchers.IO) {
        try {
            val tmpBean = boxFor
                .query()
                .equal(AudioTranscriptionBean_.id, bean.id)
                .and()
                .equal(AudioTranscriptionBean_.path, bean.path)
                .and()
                .equal(AudioTranscriptionBean_.userId, bean.userId)
                .build()
                .find()

            tmpBean.forEach {
                it.isFirstShow = false
                boxFor.put(it)
                Timber.d("RedDot updated for: $it")
            }
        } catch (e: Exception) {
            Timber.e(e, "updateIsFirstShow Failed to update path: $bean")
        }
    }

    fun delAudio(bean: MediaBean) {
        val audioBeanList = boxFor
            .query()
            .equal(AudioTranscriptionBean_.id, bean.id)
            .and()
            .equal(AudioTranscriptionBean_.path, bean.path)
            .and()
            .equal(AudioTranscriptionBean_.userId, bean.useId)
            .build()
            .find()
        // 不空才能删除
        Timber.d("delAudio 数据库删除 ${audioBeanList.size}")
        boxFor.remove(audioBeanList)
    }

    fun saveMediaLis(list: List<MediaBean>, userID: String) {
        val dbList = boxFor.query().build().find()
        val pathList = dbList.map { it.path }.toMutableList()
        val fileCreateTimeList = dbList.map { it.fileCreateTime }.toMutableList()
        val diffList = list.filter {
            !pathList.contains(it.path) ||
                !fileCreateTimeList.contains(it.fileAdded)
        }.toMutableList()
        Timber.i("saveMediaLis diffList size:${diffList.size}")
        if (diffList.isNotEmpty()) {
            val insertList = mutableListOf<AudioTranscriptionBean>()
            diffList.forEach {
                insertList.add(
                    AudioTranscriptionBean(
                        id = it.id,
                        path = it.path,
                        userId = userID,
                        fileCreateTime = it.fileAdded
                    )
                )
            }
            Timber.i("saveMediaLis 数据库插入新的数据:$insertList")
            boxFor.put(insertList)
        }
    }

    suspend fun getO95AudioListFromDb(action: (List<AudioTranscriptionBean>) -> Unit): DataSubscription =
        withContext(Dispatchers.IO) {
            // 构建查询条件
            val query = boxFor
                .query()
                .orderDesc(AudioTranscriptionBean_.fileCreateTime)
                .build()

            // 订阅查询结果并处理重复项
            val subscription = query.subscribe()
                .observer { rawList ->
                    // 处理重复数据
                    val processedList = processAndCleanDuplicates(rawList)
                    // 回调处理后的数据
                    action(processedList)
                }
            return@withContext subscription
        }

    /**
     * 处理并清理重复的 AudioTranscriptionBean
     * @param rawList 原始数据列表
     * @return 去重后的数据列表
     */
    private fun processAndCleanDuplicates(
        rawList: List<AudioTranscriptionBean>
    ): List<AudioTranscriptionBean> {
        if (rawList.isEmpty()) return rawList

        val uniqueList = mutableListOf<AudioTranscriptionBean>()
        val duplicateItems = mutableListOf<AudioTranscriptionBean>()
        val pathSet = mutableSetOf<String>()

        rawList.forEach { bean ->
            if (bean.path in pathSet) {
                duplicateItems.add(bean)
            } else {
                pathSet.add(bean.path)
                uniqueList.add(bean)
            }
        }
        return uniqueList
    }

    fun updateTaskId(bean: MediaBean, value: String, template: String) {
        val existing = boxFor.query()
            .equal(AudioTranscriptionBean_.id, bean.id)
            .and()
            .equal(AudioTranscriptionBean_.path, bean.path)
            .and()
            .equal(AudioTranscriptionBean_.userId, bean.useId)
            .build()
            .find()
        existing.forEach {
            it.transcriptionId = value
            it.summaryTemplate = template
            boxFor.put(it)
            Timber.i("updateTaskId :$value ${bean.fileName}")
        }
    }

    fun updateSummaryTaskId(bean: MediaBean, value: String) {
        val existing = boxFor.query()
            .equal(AudioTranscriptionBean_.id, bean.id)
            .and()
            .equal(AudioTranscriptionBean_.path, bean.path)
            .and()
            .equal(AudioTranscriptionBean_.userId, bean.useId)
            .build()
            .find()
        existing.forEach {
            it.summaryTaskId = value
            it.summaryStr = ""
            boxFor.put(it)
            Timber.i("updateSummaryTaskId :$value ${bean.fileName}")
        }
    }

    /**
     * 更新转写状态
     */
    fun updateTranscriptionStatus(bean: MediaBean, status: TranscriptionStatus) {
        val existing = boxFor.query()
            .equal(AudioTranscriptionBean_.id, bean.id)
            .and()
            .equal(AudioTranscriptionBean_.path, bean.path)
            .and()
            .equal(AudioTranscriptionBean_.userId, bean.useId)
            .build()
            .find()
        existing.forEach {
            it.setTranscriptionStatus(status)
            boxFor.put(it)
            Timber.i("updateTranscriptionStatus: ${status.name} for ${bean.fileName}")
        }
    }

    /**
     * 更新总结状态
     */
    fun updateSummaryStatus(bean: MediaBean, status: SummaryStatus) {
        val existing = boxFor.query()
            .equal(AudioTranscriptionBean_.id, bean.id)
            .and()
            .equal(AudioTranscriptionBean_.path, bean.path)
            .and()
            .equal(AudioTranscriptionBean_.userId, bean.useId)
            .build()
            .find()
        existing.forEach {
            it.setSummaryStatus(status)
            boxFor.put(it)
            Timber.i("updateSummaryStatus: ${status.name} for ${bean.fileName}")
        }
    }

    /**
     * 检查转写状态是否为未完成状态（进行中或等待中）
     * @param bean 媒体文件Bean
     * @return true表示转写正在进行中，false表示转写已完成或未开始
     */
    fun isTranscriptionInProgress(bean: MediaBean): Boolean {
        val existing = boxFor.query()
            .equal(AudioTranscriptionBean_.id, bean.id)
            .and()
            .equal(AudioTranscriptionBean_.path, bean.path)
            .and()
            .equal(AudioTranscriptionBean_.userId, bean.useId)
            .build()
            .findFirst()

        return existing?.let { transcriptionBean ->
            val status = transcriptionBean.getTranscriptionStatusEnum()
            status == TranscriptionStatus.UPLOADING || status == TranscriptionStatus.TRANSCRIBING
        } ?: false
    }

    /**
     * 检查总结状态是否为未完成状态（进行中）
     * @param bean 媒体文件Bean
     * @return true表示总结正在进行中，false表示总结已完成或未开始
     */
    fun isSummaryInProgress(bean: MediaBean): Boolean {
        val existing = boxFor.query()
            .equal(AudioTranscriptionBean_.id, bean.id)
            .and()
            .equal(AudioTranscriptionBean_.path, bean.path)
            .and()
            .equal(AudioTranscriptionBean_.userId, bean.useId)
            .build()
            .findFirst()

        return existing?.let { transcriptionBean ->
            val status = transcriptionBean.getSummaryStatusEnum()
            status == SummaryStatus.SUMMARIZING
        } ?: false
    }

    /**
     * 检查录音的转写和总结是否都处于最终状态（可以安全清空）
     * @param bean 媒体文件Bean
     * @return true表示可以安全清空状态，false表示有任务正在进行中不应清空
     */
    fun canSafelyClearStatus(bean: MediaBean): Boolean {
        return !isTranscriptionInProgress(bean) && !isSummaryInProgress(bean)
    }

    @Suppress("MaxLineLength")
    fun updateSpeakName(
        bean: MediaBean,
        objIds: List<Long>,
        srcSpeakerName: String,
        dstSpeakerName: String,
        fixAll: Boolean
    ): List<Long> {
        val existing = if (fixAll) {
            boxFor.query()
                .equal(AudioTranscriptionBean_.id, bean.id)
                .and()
                .equal(AudioTranscriptionBean_.path, bean.path)
                .and()
                .equal(AudioTranscriptionBean_.userId, bean.useId)
                .and()
                .equal(AudioTranscriptionBean_.speakerName, srcSpeakerName)
                .build()
                .find()
        } else {
            if (objIds.isEmpty()) return emptyList()

            // 使用 `.in()` 匹配 objId 批量更新
            boxFor.query()
                .equal(AudioTranscriptionBean_.id, bean.id)
                .and()
                .equal(AudioTranscriptionBean_.path, bean.path)
                .and()
                .equal(AudioTranscriptionBean_.userId, bean.useId)
                .and()
                .`in`(AudioTranscriptionBean_.objId, objIds.toLongArray())
                .build()
                .find()
        }
        val updatePos = mutableListOf<Long>()
        existing.forEach {
            it.speakerName = dstSpeakerName
            updatePos.add(boxFor.put(it))
            Timber.i("updateSpeakName src:$srcSpeakerName dst:$dstSpeakerName")
        }
        return updatePos
    }

    /**
     * 转写会插入多条
     */
    fun updateContent(
        bean: MediaBean,
        value: Boolean,
        contents: List<String>,
        speakId: List<Int>,
        contentSpeakerNames: List<String>,
        fileIdInCloud: String?
    ): List<AudioTranscriptionBean> {
        val existing = boxFor.query()
            .equal(AudioTranscriptionBean_.id, bean.id)
            .and()
            .equal(AudioTranscriptionBean_.path, bean.path)
            .and()
            .equal(AudioTranscriptionBean_.userId, bean.useId)
            .build()
            .find()
        boxFor.remove(existing)
        Timber.d("updateContent called ${existing.size}")
        val insertPosition = mutableListOf<AudioTranscriptionBean>()
        contents.forEachIndexed { index, content ->
            val transcriptionBean =
                AudioTranscriptionBean(id = bean.id, path = bean.path, userId = bean.useId)
            val oldItem = if (existing.isNotEmpty() && index < existing.size) {
                existing[index]
            } else {
                null
            }
            transcriptionBean.fileIdInCloud = fileIdInCloud
            transcriptionBean.transcriptionId = oldItem?.transcriptionId ?: ""
            transcriptionBean.summaryTaskId = oldItem?.summaryTaskId ?: ""
            transcriptionBean.srcLang = oldItem?.srcLang ?: ""
            transcriptionBean.srcStr = content
            transcriptionBean.summaryStr = oldItem?.summaryStr ?: ""
            transcriptionBean.isFirstShow = false
            transcriptionBean.isDistinguishSpeakers = value
            transcriptionBean.fileCreateTime = bean.fileAdded
            // 支持云端自动识别说话人，重新转写不再使用旧名称
            // val name = oldItem?.speakerName ?: ""
            val name = contentSpeakerNames[index]
            val speakerName = name.ifEmpty { "说话人${speakId[index]}" }
            transcriptionBean.speakerName = speakerName
            transcriptionBean.summaryTitle = oldItem?.summaryTitle ?: ""
            transcriptionBean.summaryTemplate = oldItem?.summaryTemplate ?: ""

            val position = boxFor.put(transcriptionBean)
            transcriptionBean.objId = position
            insertPosition.add(transcriptionBean)
        }
        return insertPosition
    }

    fun updateFileIdInCloud(bean: MediaBean, fileIdInCloud: String) {
        Timber.d("updateFileIdInCloud, fileIdInCloud -> $fileIdInCloud")
        val existing = boxFor.query()
            .equal(AudioTranscriptionBean_.id, bean.id)
            .and()
            .equal(AudioTranscriptionBean_.path, bean.path)
            .and()
            .equal(AudioTranscriptionBean_.userId, bean.useId)
            .build()
            .find()
        existing.forEach {
            it.fileIdInCloud = fileIdInCloud
            boxFor.put(it)
        }
    }

    fun updateIsFocusSpeaker(
        bean: MediaBean,
        objIds: List<Long>,
        srcSpeakerName: String,
        isFocusSpeaker: Boolean,
        fixAll: Boolean
    ): List<Long> {
        val existing = if (fixAll) {
            boxFor.query()
                .equal(AudioTranscriptionBean_.id, bean.id)
                .and()
                .equal(AudioTranscriptionBean_.path, bean.path)
                .and()
                .equal(AudioTranscriptionBean_.userId, bean.useId)
                .and()
                .equal(AudioTranscriptionBean_.speakerName, srcSpeakerName)
                .build()
                .find()
        } else {
            if (objIds.isEmpty()) return emptyList()

            // 使用 `.in()` 匹配 objId 批量更新
            boxFor.query()
                .equal(AudioTranscriptionBean_.id, bean.id)
                .and()
                .equal(AudioTranscriptionBean_.path, bean.path)
                .and()
                .equal(AudioTranscriptionBean_.userId, bean.useId)
                .and()
                .`in`(AudioTranscriptionBean_.objId, objIds.toLongArray())
                .build()
                .find()
        }
        val updatePos = mutableListOf<Long>()
        existing.forEach {
            it.isFocusSpeaker = isFocusSpeaker
            updatePos.add(boxFor.put(it))
            Timber.i(
                "updateIsFocusSpeaker name -> $srcSpeakerName" +
                    ", isFocusSpeaker -> $isFocusSpeaker"
            )
        }
        return updatePos
    }

    /**
     * 清空转写内容
     */
    fun clearTranscribeContent(bean: MediaBean) {
        val existing = boxFor.query()
            .equal(AudioTranscriptionBean_.id, bean.id)
            .and()
            .equal(AudioTranscriptionBean_.path, bean.path)
            .and()
            .equal(AudioTranscriptionBean_.userId, bean.useId)
            .build()
            .find()
        existing.forEach {
            it.srcStr = ""
            it.speakerName = ""
            boxFor.put(it)
        }
    }

    /**
     * 查询未完成的转写任务
     * 条件：有transcriptionId但srcStr为空或null的记录
     */
    suspend fun getUncompletedTranscriptionTasks(): List<AudioTranscriptionBean> = withContext(Dispatchers.IO) {
        try {
            val allTasks = boxFor.query()
                .notNull(AudioTranscriptionBean_.transcriptionId)
                .build()
                .find()

            // 过滤出转写内容为空且transcriptionId不为空的任务
            val uncompletedTasks = allTasks.filter { task ->
                !task.transcriptionId.isNullOrEmpty() && task.srcStr.isNullOrEmpty()
            }

            Timber.i("查询到未完成的转写任务数量: ${uncompletedTasks.size}")
            for (task in uncompletedTasks) {
                Timber.d("未完成任务: taskId=${task.transcriptionId}, path=${task.path}")
            }

            return@withContext uncompletedTasks
        } catch (e: Exception) {
            Timber.e(e, "查询未完成转写任务失败")
            return@withContext emptyList()
        }
    }

    /**
     * 根据转写任务获取对应的MediaBean信息
     */
    suspend fun getMediaBeanByTranscriptionTask(task: AudioTranscriptionBean): MediaBean? = withContext(Dispatchers.IO) {
        try {
            val mediaBoxFor = DbHelper.getBoxStore().boxFor(MediaBean::class.java)
            val mediaBean = mediaBoxFor.query()
                .equal(MediaBean_.id, task.id)
                .and()
                .equal(MediaBean_.path, task.path)
                .and()
                .equal(MediaBean_.useId, task.userId)
                .build()
                .findFirst()

            if (mediaBean != null) {
                Timber.d("找到对应的MediaBean: id=${mediaBean.id}, path=${mediaBean.path}")
            } else {
                Timber.w("未找到对应的MediaBean: id=${task.id}, path=${task.path}")
            }

            return@withContext mediaBean
        } catch (e: Exception) {
            Timber.e(e, "获取MediaBean失败: id=${task.id}, path=${task.path}")
            return@withContext null
        }
    }

    /**
     * 清空总结内容
     */
    fun clearSummary(bean: MediaBean, template: String) {
        val existing = boxFor.query()
            .equal(AudioTranscriptionBean_.id, bean.id)
            .and()
            .equal(AudioTranscriptionBean_.path, bean.path)
            .and()
            .equal(AudioTranscriptionBean_.userId, bean.useId)
            .build()
            .find()
        existing.forEach {
            it.summaryTitle = ""
            it.summaryStr = ""
            it.summaryTemplate = template
            it.summaryErrorCode = 0
            boxFor.put(it)
        }
    }

    @Suppress("LongParameterList")
    fun updateSummaryContent(
        bean: MediaBean,
        content: String,
        focusSpeakerSummaryStr: String?,
        summaryTitle: String,
        template: String,
        summaryErrorCode: Int
    ) {
        val existing = boxFor.query()
            .equal(AudioTranscriptionBean_.id, bean.id)
            .and()
            .equal(AudioTranscriptionBean_.path, bean.path)
            .and()
            .equal(AudioTranscriptionBean_.userId, bean.useId)
            .build()
            .find()
        existing.forEach {
            it.summaryStr = content
            it.focusSpeakerSummaryStr = focusSpeakerSummaryStr
            it.summaryTitle = summaryTitle
            it.summaryTemplate = template
            it.summaryErrorCode = summaryErrorCode
            boxFor.put(it)
            Timber.i(
                "updateSummaryContent : ${bean.fileName}, content -> $content" +
                    ", focusSpeakerSummaryStr -> $focusSpeakerSummaryStr"
            )
        }
    }

    fun findCorrespondBean(bean: MediaBean): List<AudioTranscriptionBean> {
        return boxFor.query()
            .equal(AudioTranscriptionBean_.id, bean.id)
            .and()
            .equal(AudioTranscriptionBean_.path, bean.path)
            .and()
            .equal(AudioTranscriptionBean_.userId, bean.useId)
            .build()
            .find()
    }
}
