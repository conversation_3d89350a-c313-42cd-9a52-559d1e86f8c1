plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

apply from: "../../library.gradle"

android {
    namespace 'com.xiaomi.aivs'
}

dependencies {
//    api project(path: ':module_basic:library_base')
    api deps.kotlin_stdlib
    api deps.androidx_core_core_ktx
    api deps.kotlinx_coroutines_core

    api deps.timber
    api deps.mmkv_static
    
    api deps.okhttp3
    api (deps.okhttp_logging_interceptor) {
        exclude group: 'com.squareup.okhttp3'
    }

    api(deps.retrofit2) {
        exclude group: 'com.squareup.okhttp3'
    }
    api deps.retrofit_convert_gson
    api deps.retrofit_convert_moshi
    api deps.retrofit_converter_scalars

    api project(path: ':libs:miaivs')
    api 'com.xiaomi:ai-api-spec-java:0.1.354'
    api project(path: ':module_basic:library_music')
    implementation project(path: ':module_basic:library_base')
    implementation project(':module_basic:library_base_common')

    api deps.xiaomi_passportsdk_account_sso
//    api 'com.xiaomi.account:oauth-android:latest.release'
    api(deps.xiaomi_passportsdk_account_oauth) {
        exclude group: 'com.xiaomi.account', module: 'privacy-data-device-id'
    }
    api 'com.miui.voiceassist:voiceassist-bridge:1.0.16'
    implementation 'com.google.firebase:firebase-crashlytics-buildtools:3.0.2'
    implementation project(':module_basic:library_db')

    def ktor_version = "3.0.0-beta-1"
    implementation("io.ktor:ktor-client-core:$ktor_version")
    implementation("io.ktor:ktor-client-okhttp:$ktor_version")
    implementation("io.ktor:ktor-client-websockets:$ktor_version")
    implementation("io.ktor:ktor-client-logging:$ktor_version")
    implementation("io.ktor:ktor-client-content-negotiation:$ktor_version")
    implementation("io.ktor:ktor-serialization-kotlinx-json:$ktor_version")
    implementation deps.androidx_media3_exoplayer
//    implementation("androidx.media3:media3-exoplayer:1.0.0")
//    implementation("androidx.media3:media3-exoplayer-dash:1.0.0")

    implementation 'com.github.rosuH:MPG123-Android:0.1.2'
}