@file:Suppress("TooManyFunctions")

package com.xiaomi.aivs.engine.proxy

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import androidx.annotation.Keep
import com.superhexa.music.utils.LiteJsonUtils.toJson
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.ACTION_NOTIFY_BROADCAST_FEED_BACK
import com.superhexa.supervision.library.base.basecommon.tools.FeedBackUtil
import com.xiaomi.ai.android.capability.AuthCapability
import com.xiaomi.ai.android.capability.ConnectionCapability
import com.xiaomi.ai.android.capability.ErrorCapability
import com.xiaomi.ai.android.capability.InstructionCapability
import com.xiaomi.ai.android.capability.LargeModelCapability
import com.xiaomi.ai.android.capability.SpeechSynthesizerCapability
import com.xiaomi.ai.android.core.Engine
import com.xiaomi.ai.android.helper.ProtoHelper
import com.xiaomi.ai.api.AIApiConstants
import com.xiaomi.ai.api.Execution
import com.xiaomi.ai.api.General
import com.xiaomi.ai.api.Settings
import com.xiaomi.ai.api.SpeechRecognizer
import com.xiaomi.ai.api.SpeechSynthesizer
import com.xiaomi.ai.api.common.APIUtils
import com.xiaomi.ai.api.common.Event
import com.xiaomi.ai.api.common.EventPayload
import com.xiaomi.ai.api.common.Instruction
import com.xiaomi.ai.api.common.InstructionPayload
import com.xiaomi.ai.error.AivsError
import com.xiaomi.ai.log.Logger
import com.xiaomi.aivs.AiSpeechEngine
import com.xiaomi.aivs.BuildConfig
import com.xiaomi.aivs.R
import com.xiaomi.aivs.bridge.PhoneBridge
import com.xiaomi.aivs.config.ConfigCache
import com.xiaomi.aivs.data.DialogNode
import com.xiaomi.aivs.data.model.AccountConfig
import com.xiaomi.aivs.data.model.AuthConfig
import com.xiaomi.aivs.engine.context.RequestContextHolder
import com.xiaomi.aivs.engine.event.EventBuilder
import com.xiaomi.aivs.engine.process.InstructionProcessor
import com.xiaomi.aivs.engine.process.TtsDependency
import com.xiaomi.aivs.engine.process.stream.StreamProcessCallback
import com.xiaomi.aivs.engine.state.AudioFocusState
import com.xiaomi.aivs.engine.state.EngineStateMachine
import com.xiaomi.aivs.net.OauthRetrofitFactory
import com.xiaomi.aivs.net.repository.OauthAccountRepository
import com.xiaomi.aivs.net.retrofit.service.OauthApiService
import com.xiaomi.aivs.player.AudioPlayer
import com.xiaomi.aivs.player.MPLayer
import com.xiaomi.aivs.player.SoundPlayer
import com.xiaomi.aivs.player.UtteranceListener
import com.xiaomi.aivs.track.EventTrack
import com.xiaomi.aivs.track.EventTrackKv
import com.xiaomi.aivs.utils.ExtensionFun.getOrNull
import com.xiaomi.aivs.utils.FeedbackAsrUtil
import com.xiaomi.aivs.utils.SpeechEngineHelper
import com.xiaomi.aivs.utils.toHex
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.launch
import kotlinx.coroutines.newSingleThreadContext
import kotlinx.coroutines.runBlocking
import timber.log.Timber
import java.util.LinkedList
import java.util.UUID

class SpeechEngineProxyImpl(
    private val context: Context
) : ISpeechEngineProxy, UtteranceListener, StreamProcessCallback {

    private val coroutineExceptionHandler = CoroutineExceptionHandler { _, exception ->
        Timber.d("SpeechEngineProxy Caught: $exception")
    }

    @OptIn(DelicateCoroutinesApi::class, ExperimentalCoroutinesApi::class)
    private val workScope = CoroutineScope(
        newSingleThreadContext("SpeechEngineProxy") + coroutineExceptionHandler
    )

    private var aiEngine: Engine? = null
    private var imageEngine: Engine? = null
    private var ttsEngine: Engine? = null
    private val oauthAccountRepository =
        OauthAccountRepository(OauthRetrofitFactory.provideService(OauthApiService::class.java))
    private val phoneBridge = PhoneBridge()
    private val contextHolder = RequestContextHolder(phoneBridge)
    private val eventBuilder = EventBuilder(contextHolder)

    private var instructionProcessor: InstructionProcessor? = null
    private val audioTrack = AudioPlayer(this)
    private val mpLayer = MPLayer(AiSpeechEngine.INSTANCE.appContext, this)

    private var curTtsId: String? = ""

    override fun init(context: Context, config: AuthConfig) {
        Timber.tag(TAG).d("init:$config")
        if (BuildConfig.DEBUG) {
            Logger.setLogLevel(Logger.LOG_LEVEL_DEBUG)
        } else {
            Logger.setLogLevel(Logger.LOG_LEVEL_DEBUG)
        }
        workScope.launch {
            phoneBridge.init(context)
            ConfigCache.cleanToken()
            ConfigCache.authConfig = config
            aiEngine = SpeechEngineHelper.createDeviceOauthEngine(
                context,
                config,
                "$AI_ENGINE_NAME_PREFIX${UUID.randomUUID().toString().replace("-", "")}"
            )
            imageEngine = SpeechEngineHelper.createDeviceOauthEngine(
                context,
                config,
                IMAGE_ENGINE_NAME,
                rejectPush = true
            )
            ttsEngine = SpeechEngineHelper.createDeviceOauthEngine(
                context,
                config,
                TTS_ENGINE_NAME,
                rejectPush = true
            )
        }
    }

    override fun getAiEngine(): Engine? {
        return aiEngine
    }

    override fun startup(accountConfig: AccountConfig) {
        Timber.tag(TAG).d("startup:$accountConfig")
        workScope.launch {
            ConfigCache.accountConfig = accountConfig
            instructionProcessor = InstructionProcessor(
                context = context,
                phoneBridge = phoneBridge,
                callback = this@SpeechEngineProxyImpl
            )

            registerCapability(engine = aiEngine, isMain = true, engineType = EngineType.MAIN)
            registerCapability(engine = imageEngine, engineType = EngineType.IMAGE)
            registerCapability(
                engine = ttsEngine,
                requestJudgment = false,
                engineType = EngineType.TTS
            )

            val result = aiEngine?.start()
            val imageResult = imageEngine?.start()
            val ttsResult = ttsEngine?.start()
            Timber.tag(TAG).d("init result:$result,$imageResult,$ttsResult")
        }
    }

    private fun registerCapability(
        engine: Engine?,
        requestJudgment: Boolean = true,
        isMain: Boolean = false,
        engineType: EngineType
    ) {
        Timber.tag(TAG).d("registerCapability:$requestJudgment,$isMain")
        if (isMain) {
            engine?.registerCapability(connectionCapability)
            engine?.registerCapability(errorCapability)
        }
        engine?.registerCapability(authCapability)
        if (requestJudgment) {
            engine?.registerCapability(NormalCapability(engineType))
            engine?.registerCapability(StreamCapability(engineType))
        }
        engine?.registerCapability(TtsCapability(requestJudgment = requestJudgment))
    }

    override fun postEvent(
        payload: EventPayload,
        requestId: String?,
        params: Map<String, String>?,
        preRunnable: Runnable?,
        withContext: Boolean
    ): String {
        val event = requestId?.let {
            eventBuilder.buildEvent(
                payload = payload,
                requestId = requestId,
                params = params,
                withContext = withContext
            )
        } ?: run {
            eventBuilder.buildEvent(
                payload = payload,
                params = params,
                withContext = withContext
            )
        }
        preRunnable?.run()
        workScope.launch {
            val isSuccess = aiEngine?.postEvent(event)
            Timber.tag(TAG).d("postEvent:${payload.toJson()},$requestId,$isSuccess")
        }
        return event.id
    }

    override fun postEvent(event: Event<*>) {
        workScope.launch {
            val isSuccess = aiEngine?.postEvent(event)
            Timber.tag(TAG).d("postEvent:$event,$isSuccess")
        }
    }

    override fun postFeedBackEvent() {
        val disableList: MutableList<Execution.RequestControlType> = LinkedList()

        // 可以根据需要禁掉TTS或（和）NLP返回
        disableList.add(Execution.RequestControlType.TTS) // 禁掉TTS
        disableList.add(Execution.RequestControlType.NLP) // 禁掉NLP
        val requestControl = Execution.RequestControl()
        requestControl.setDisabled(disableList)

        val payloadRecognize = SpeechRecognizer.Recognize()
            .setAsr(Settings.AsrConfig().setVad(false))
        val event = APIUtils.buildEvent(payloadRecognize)
        event.addContext(APIUtils.buildContext(requestControl))
        workScope.launch {
            val isSuccess = aiEngine?.postEvent(event)
            Timber.d("postFeedBackEvent isSuccess: $isSuccess")
        }
    }

    override fun saveFeedBackAsr(asr: String, requestId: String) {
        FeedbackAsrUtil.writeToCacheFile(context, asr, requestId)
    }

    override fun sendTaskBroadcast(requestId: String, requestType: String) {
        Timber.d("sendTaskBroadcast packageName: ${context.packageName}")
        val intent = Intent(ACTION_NOTIFY_BROADCAST_FEED_BACK).apply {
            setPackage(context.packageName)
            putExtra(BundleKey.EXTRA_REQUEST_ID, requestId)
            putExtra(BundleKey.FEED_EXTRA_TYPE, requestType)
        }
        context.sendBroadcast(intent)
    }

    override fun postImageEvent(
        payload: EventPayload,
        requestId: String?,
        isFetchDeviceInfo: Boolean,
        params: Map<String, String>?
    ): String {
        val event = requestId?.let {
            eventBuilder.buildEvent(payload = payload, requestId = requestId, params = params)
        } ?: run {
            eventBuilder.buildEvent(payload = payload, params = params)
        }
        event.header.setIsPassive(true)
        event.header.setIsFetchDeviceInfo(isFetchDeviceInfo)
        workScope.launch {
            val isSuccess = imageEngine?.postEvent(event)
            Timber.tag(TAG).d("postImageEvent:$isFetchDeviceInfo,${payload.toJson()},$isSuccess")
        }
        return event.id
    }

    override fun postSpeechData(bytes: ByteArray?, offset: Int, length: Int, isFinal: Boolean) {
//        Timber.tag(TAG).d("postSpeechData:${bytes?.toHex()},$length,$isFinal")
        workScope.launch {
            val success = aiEngine?.postData(bytes, offset, length, isFinal)
//            Timber.tag(TAG).d("postSpeechData result:$success, $aiEngine")
        }
    }

    override fun postImageData(
        requestId: String,
        format: String,
        size: Pair<Int, Int>,
        chunk: Pair<Int, Int>,
        bytes: ByteArray?
    ) {
        workScope.launch {
            val image: ProtoHelper.Image =
                ProtoHelper.Image(format, size.first, size.second)
            val data = image.pack(bytes, chunk.first, chunk.first == (chunk.second - 1))
            val success = imageEngine?.postRawData(data, 0, data.size)
            Timber.tag(TAG).d(
                "postImageData:$requestId,$format,$size,$chunk,${bytes?.size},$success"
            )
        }
    }

    override fun isTtsSpeaking(): Boolean {
        val playing = audioTrack.isPlaying()
        // GTKGLASS-13881 只判断普通TTS
        val mpLayerPlaying = mpLayer.isPlaying() && AudioType.TTS == mpLayer.getCurrentAudioType()
        Timber.tag(TAG).d("isTtsSpeaking:$playing,$mpLayerPlaying")
        return playing || mpLayerPlaying
    }

    override fun startTts(text: String, params: Map<String, String>?): String? {
        val event = eventBuilder.buildEvent(SpeechSynthesizer.Synthesize(text), params = params)
        workScope.launch {
            val isSuccess = ttsEngine?.postEvent(event)
            Timber.tag(TAG).d("startTts:$text,$params,$isSuccess,$event")
        }
        return event.id
    }

    override fun stopTts(dialogId: String?, stopOptions: TtsStopOptions) {
        val longAudioPausing = mpLayer.isLongAudioPausing()
        val longAudioPlaying = mpLayer.isLongAudioPlaying()
        val userPause = mpLayer.isUserPause()
        Timber.tag(TAG).d(
            "stopTts: $dialogId, resume=$stopOptions" +
                " paused=$longAudioPausing, playing=$longAudioPlaying, userPause=$userPause"
        )

        when {
            stopOptions.needStopMediaPlayer == true -> stopMediaPlayer()
            stopOptions.needResumeMediaPlayer == true && longAudioPausing && !userPause -> {
                resumeMediaPlayer("isNeedAudioResume")
            }
            !longAudioPausing && !longAudioPlaying -> stopMediaPlayer()
        }
        dialogId?.let { audioTrack.stopWithId(it) } ?: audioTrack.stop(stopOptions.calledFrom)
        AudioFocusState.doFocusAbandon(AiSpeechEngine.INSTANCE.appContext, "stopTts")
    }

    @SuppressLint("ImplicitSamInstance")
    override fun playTipSound(resourceId: Int, complete: (() -> Unit)?) {
        Timber.tag(TAG).d("playTipSound")
        stopTts(
            stopOptions = TtsStopOptions(
                calledFrom = "playTipSound",
                needResumeMediaPlayer = null,
                needStopMediaPlayer = null,
                stopReason = null
            )
        )
        mpLayer.playTipSound(resourceId, complete)
    }

    override fun resumeMediaPlayer(reason: String?) {
        Timber.tag(TAG).d("resumeMediaPlayer $reason")
        mpLayer.resume()
    }

    override fun stopMediaPlayer() {
        Timber.tag(TAG).d("stopMediaPlayer")
        mpLayer.stopAll("stopMediaPlayer")
    }

    override fun pauseMediaPlayer(reason: String?) {
        Timber.tag(TAG).d("pauseMediaPlayer $reason")
        if (reason != null) {
            mpLayer.userPauseChane(reason, true)
        }
        mpLayer.pause()
    }

    override fun handleMediaControl(payload: InstructionPayload) {
        Timber.tag(TAG).d("handleMediaControl")
        mpLayer.handleMediaControl(payload)
    }

    override fun isLongAudioPausing(): Boolean {
        return mpLayer.isLongAudioPausing()
    }

    override fun isLongAudioPlaying(): Boolean {
        return mpLayer.isLongAudioPlaying()
    }

    override fun interrupt(stopTts: Boolean, reason: String?, stopOptions: TtsStopOptions) {
        Timber.tag(TAG).d("interrupt:$stopTts")
        Timber.tag("Test").d("interrupt")
        workScope.launch {
            aiEngine?.interrupt()
            imageEngine?.interrupt()
            ttsEngine?.interrupt()
        }
    }

    override fun releaseEngine() {
        Timber.tag(TAG).d("releaseEngine")
        workScope.launch {
            aiEngine?.clearUserData()
            imageEngine?.clearUserData()
            ttsEngine?.clearUserData()
            aiEngine?.release()
            imageEngine?.release()
            ttsEngine?.release()
            aiEngine = null
            imageEngine = null
            ttsEngine = null
            mpLayer.release()
        }
    }

    override fun destroy() {
        Timber.tag(TAG).d("destroy")
        phoneBridge.release(context)
    }

    private val connectionCapability = object : ConnectionCapability() {

        override fun onConnected() {
            Timber.tag(TAG).d("onConnected")
            AiSpeechEngine.INSTANCE.onConnectState(true)
        }

        override fun onDisconnected() {
            Timber.tag(TAG).d("onDisconnected")
            AiSpeechEngine.INSTANCE.onConnectState(false)
        }

        override fun onGetSSID(): String {
            Timber.tag(TAG).d("onGetSSID")
            return ""
        }
    }

    private val authCapability = object : AuthCapability() {
        override fun onGetOAuthCode(): String {
            Timber.tag(TAG).d("onGetOAuthCode:${Thread.currentThread()}")
            return runBlocking {
//                oauthAccountRepository.requestOauthCode(activity) ?: ""
                ""
            }
        }

        override fun onGetToken(authType: Int, needRefresh: Boolean): String {
            Timber.tag(TAG).d("onGetToken:$authType,$needRefresh,${Thread.currentThread()}")
            return runBlocking {
                oauthAccountRepository.getAccessTokenToken(context, needRefresh) ?: ""
            }
        }
    }

    inner class NormalCapability(private val engineType: EngineType) : InstructionCapability() {
        override fun process(instruction: Instruction<*>?): Boolean {
            Timber.tag(TAG).d("onInstruction:${instruction?.header?.fullName}")
            // 场景复现平台日志拉取
            processFetchDeviceLog(instruction)
            if (!EngineStateMachine.isIdle()) {
                instruction?.let { instructionProcessor?.process(instruction, false, engineType) }
            } else {
                Timber.d("onInstruction dialogState:${EngineStateMachine.dialogState()}")
            }
            return true
        }

        override fun processBinary(data: ByteArray?, p1: String?): Boolean {
            Timber.tag(TAG).d("processBinary:${data?.toHex()}")
            return super.processBinary(data, p1)
        }

        private fun processFetchDeviceLog(instruction: Instruction<*>?) {
            Timber.tag(TAG).d("instruction: $instruction")
            if (instruction?.header?.fullName != AIApiConstants.General.Push) return

            val pushPayload = instruction.payload as? General.Push ?: return
            val idParam = pushPayload.msgMeta.getOrNull()?.id ?: return

            APIUtils.readInstructions(pushPayload.instructions)
                .filter { it?.fullName == AIApiConstants.General.FetchDeviceLog }
                .forEach { _ ->
                    FeedBackUtil.startSceneLogWorker(context, idParam)
                }
        }
    }

    inner class StreamCapability(private val engineType: EngineType) : LargeModelCapability() {
        private var ttsId: String? = ""

        override fun onStreamInstruction(instruction: Instruction<*>?) {
            Timber.tag(TAG).d("onStreamInstruction:${instruction?.header?.fullName}")
            if (!EngineStateMachine.isIdle()) {
                AiSpeechEngine.INSTANCE.onStreamInstruction(instruction)
                instruction?.let { instructionProcessor?.process(instruction, isStream = true, engineType) }
            } else {
                Timber.d("onStreamInstruction dialogState:${EngineStateMachine.dialogState()}")
            }
        }

        override fun onTtsStart(sampleRate: Int, dialogId: String?) {
            Timber.tag(TAG).d("onTtsStart:$sampleRate,$dialogId")
            if (isTtsPlayIgnore()) return
            EventTrack.onEventTrackTime(
                dialogId = dialogId,
                key = EventTrackKv.TTS_STREAM_START
            )
            mpLayer.stopAll("onTtsStart")
            SoundPlayer.INSTANCE.stop(R.raw.standby_enter)
            ttsId = dialogId
            curTtsId = dialogId
            audioTrack.stop("onTtsStart")
            audioTrack.play()
        }

        override fun onTtsFinish(dialogId: String?) {
            Timber.tag(TAG).d("onTtsFinish:$dialogId")
            audioTrack.onReceiveDataEnd("$dialogId")
        }

        override fun onTtsData(data: ByteArray?, dialogId: String?) {
            Timber.tag(TAG).d("onTtsData:${data?.size}")
            if (isTtsPlayIgnore()) return
            EventTrack.onEventTrackTime(
                dialogId = dialogId,
                key = EventTrackKv.TTS_FIRST_PACK_RECEIVED
            )
            data?.let {
                if (ttsId == curTtsId) {
                    audioTrack.onReceiveData("$curTtsId", data)
                }
            }
        }
    }

    /**
     * @param requestJudgment TTS播报是否需要判定,TTS无需处理.
     */
    inner class TtsCapability(
        private val requestJudgment: Boolean = true
    ) : SpeechSynthesizerCapability() {
        private var ttsId: String? = ""

        // AivsConfig.Tts.ENABLE_PLAY_DIALOG_ID:false  时回调.
        override fun onPlayStart(sampleRate: Int) {
            Timber.tag(TAG).d("onPlayStart")
        }

        // AivsConfig.Tts.ENABLE_PLAY_FINISH_DIALOG_ID:false 时回调.
        override fun onPlayFinish() {
            Timber.tag(TAG).d("onPlayFinish")
        }

        override fun onPlayStart(sampleRate: Int, dialogId: String?) {
            Timber.tag(TAG).d("onPlayStart:$sampleRate，$dialogId,$requestJudgment")
            if (requestJudgment && isTtsPlayIgnore()) return
            if (mpLayer.isLongAudioPlaying()) {
                mpLayer.pause()
            } else {
                mpLayer.stopAll("onPlayStart")
            }
            SoundPlayer.INSTANCE.stop(R.raw.standby_enter)
            ttsId = dialogId
            curTtsId = dialogId
            audioTrack.stop("onPlayStart")
            audioTrack.play()
        }

        override fun onPlayFinish(dialogId: String?) {
            Timber.tag(TAG).d("onPlayFinish:$dialogId,$requestJudgment")
            if (requestJudgment && isTtsPlayIgnore()) return
            audioTrack.onReceiveDataEnd("$ttsId")
        }

        override fun onPcmData(data: ByteArray?) {
            Timber.tag(TAG).d("onPcmData:$ttsId,$curTtsId,${data?.size},$requestJudgment")
            if (requestJudgment && isTtsPlayIgnore()) return
            EventTrack.onEventTrackTime(
                dialogId = ttsId,
                key = EventTrackKv.TTS_FIRST_PACK_RECEIVED
            )
            data?.let {
                // 规避并发同时发送数据.
                if (ttsId == curTtsId) {
                    audioTrack.onReceiveData("$ttsId", data)
                }
            }
        }
    }

    private val errorCapability: ErrorCapability = object : ErrorCapability() {
        override fun onError(aivsError: AivsError) {
            Timber.tag(TAG).d("onError:${aivsError.errorCode},${aivsError.errorMessage}")
            ConfigCache.cleanToken()
            aivsError.apply {
                EventTrack.onEventTrack(
                    dialogId = eventId,
                    key = EventTrackKv.STATE_ERROR_CODE,
                    value = errorCode
                )
                EventTrack.onEventTrack(
                    dialogId = eventId,
                    key = EventTrackKv.STATE_ERROR_MSG,
                    value = errorMessage
                )
            }
            // EngineStateMachine.onDisconnected()
            // listener.onConnectState(false)
        }
    }

    override fun onReceiveTtsUrl(
        dialogId: String?,
        urls: List<String>,
        audioType: AudioType
    ) {
        Timber.tag(TAG).d("onReceiveTtsUrl:$dialogId,$urls")
        if (isTtsPlayIgnore()) return
        dialogId?.let {
            mpLayer.stopAll("onReceiveTtsUrl")
            audioTrack.stop("onReceiveTtsUrl")
            SoundPlayer.INSTANCE.stop(R.raw.standby_enter)
            when (audioType) {
                AudioType.TTS -> {
                    mpLayer.play(MPLayer.TtsSource(dialogId, urls)) {
                        Timber.tag(TAG).d(
                            "mpLayer play complete: dialogId -> $dialogId" +
                                ", audioType -> TTS"
                        )
                    }
                }

                AudioType.LONG_AUDIO -> {
                    mpLayer.play(MPLayer.LongAudioSource(dialogId, urls)) {
                        Timber.tag(TAG).d(
                            "mpLayer play complete: dialogId -> $dialogId" +
                                ", audioType -> LONG_AUDIO"
                        )
                    }
                }

                else -> {
                    Timber.tag(TAG).d("else $audioType")
                }
            }
        }
    }

    private fun isTtsPlayIgnore(): Boolean {
        val isIgnore = EngineStateMachine.isIdle()
        Timber.tag(TAG).d("isTtsPlayIgnore:$isIgnore")
        return isIgnore
    }

    override fun onUtteranceStart(utteranceId: String?, isUrl: Boolean, isLocalCorpus: Boolean) {
        Timber.tag(TAG).d("onUtteranceStart:$utteranceId,$isUrl,$isLocalCorpus")
        if (!isLocalCorpus) {
            EventTrack.onEventTrackTime(
                dialogId = utteranceId,
                key = if (isUrl) EventTrackKv.TTS_URL_START else EventTrackKv.TTS_STREAM_START
            )
            if (AiSpeechEngine.INSTANCE.getCurrentMediaType()
                == SpeechEngineProxyImpl.Companion.AudioType.LONG_AUDIO
            ) {
                Timber.tag(TAG).d("onUtteranceStart:LONG_AUDIO")
            } else {
                AiSpeechEngine.INSTANCE.cancelTimer("TTS开始播报.")
            }
        }
        AudioFocusState.doFocusRequest(context = context, reason = "onUtteranceStart")
        AiSpeechEngine.INSTANCE.onUtteranceStart(utteranceId, isUrl, isLocalCorpus)
    }

    override fun onUtteranceDone(utteranceId: String?, isUrl: Boolean, isLocalCorpus: Boolean) {
        Timber.tag(TAG).d("onUtteranceDone:$utteranceId,$isUrl,$isLocalCorpus")
        if (!isLocalCorpus) {
            AiSpeechEngine.INSTANCE.restartTimer(
                "TTS播报结束.",
                AiSpeechEngine.INSTANCE.countdownTime()
            )
            utteranceId?.let { TtsDependency.onTtsPlayDone(utteranceId) }
            EngineStateMachine.onDialogNode(DialogNode.ASR_RESTART)
        }
        AudioFocusState.doFocusAbandon(
            context = context,
            reason = "onUtteranceDone",
            delayMs = ASR_RESTART_DELAY
        )
        AiSpeechEngine.INSTANCE.onUtteranceDone(utteranceId, isUrl, isLocalCorpus)
        if (mpLayer.isLongAudioPausing()) {
            resumeMediaPlayer("onUtteranceDone")
        }
    }

    override fun onUtteranceStop(utteranceId: String?, isUrl: Boolean) {
        Timber.tag(TAG).d("onUtteranceStop:$utteranceId,$isUrl")
        AudioFocusState.doFocusAbandon(context = context, reason = "onUtteranceStop")
        AiSpeechEngine.INSTANCE.onUtteranceStop(utteranceId, isUrl)
        utteranceId?.let { TtsDependency.onTtsPlayStop(utteranceId) }
        if (mpLayer.isLongAudioPausing()) {
            resumeMediaPlayer("onUtteranceStop")
        }
    }

    override fun playVoiceFinish() {
        Timber.tag(TAG).d("playVoiceFinish")
        audioTrack.stop("playVoiceFinish")
    }

    override fun playVoiceStart() {
        Timber.tag(TAG).d("playVoiceStart")
        audioTrack.stop("playVoiceStart")
        audioTrack.play()
    }

    override fun receivePcmData(data: ByteArray) {
        Timber.tag(TAG).d("receivePcmData")
        audioTrack.onReceiveData("", data)
    }

    companion object {
        private const val TAG = "SpeechEngine_Android"
        private const val ASR_RESTART_DELAY = 2000L
        private const val AI_ENGINE_NAME_PREFIX = "main_"
        private const val IMAGE_ENGINE_NAME = "image_engine_android"
        private const val TTS_ENGINE_NAME = "tts_engine_android"
        private const val FEED_BACK_ENGINE_NAME = "feed_back_android"

        enum class EngineType {
            MAIN,
            IMAGE,
            TTS
        }

        @Keep
        enum class AudioType {
            TTS,
            MUSIC,
            LONG_AUDIO
        }

        @Keep
        data class TtsStopOptions(
            val calledFrom: String,
            val needResumeMediaPlayer: Boolean? = false,
            val needStopMediaPlayer: Boolean? = false,
            val stopReason: String?
        )
    }
}
